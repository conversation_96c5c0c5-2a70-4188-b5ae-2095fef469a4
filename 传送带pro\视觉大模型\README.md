# DobotVisionStudio 工件视觉识别系统

基于DobotVisionStudio4.1.2的完整工件视觉识别解决方案，集成了多种检测算法和机器人控制接口。

## 系统特性

- 🔍 **多算法支持**: 圆形检测、模板匹配、特征匹配、边缘检测
- 🤖 **机器人集成**: 直接输出机器人坐标，支持TCP通信
- 🎯 **高精度检测**: 亚像素级定位精度，检测速度 < 5ms
- 🔧 **模块化设计**: 可扩展的算法模块，支持自定义开发
- 📊 **实时处理**: 支持连续检测和实时图像处理
- 🖥️ **可视化界面**: 提供完整的GUI演示程序
- 🔌 **硬件集成**: 基于plus.py协议，直接连接DobotVisionStudio硬件
- 📡 **双向通信**: 支持触发拍照和接收检测结果

## 文件结构

```
DobotVisionStudio_视觉识别/
├── DobotVisionStudio_视觉识别核心代码分析.md  # 详细技术分析文档
├── VisionWorkpieceDetector.cs                # C# 核心检测类
├── vision_workpiece_detector.py              # Python 检测接口
├── vision_demo.py                            # GUI 演示程序
├── vision_config.json                        # 系统配置文件
├── README.md                                 # 使用说明
└── plus.py                                   # 原始集成接口
```

## 快速开始

### 1. 环境要求

**C# 版本**:
- .NET Framework 4.5+
- VisionMaster SDK
- DobotVisionStudio4.1.2

**Python 版本**:
- Python 3.7+
- OpenCV 4.0+
- NumPy
- Tkinter (GUI)

### 2. 安装依赖

```bash
# Python 依赖
pip install opencv-python numpy pillow

# 或使用 requirements.txt
pip install -r requirements.txt
```

### 3. 运行演示程序

**推荐方式 - 使用启动脚本**:
```bash
# 运行启动脚本，提供多种选项
python start_vision_system.py
```

**直接启动方式**:
```bash
# 启动 GUI 演示程序 (集成硬件连接)
python vision_demo.py

# 或直接运行检测器
python vision_workpiece_detector.py
```

## 使用方法

### C# 版本使用

```csharp
// 1. 初始化检测器
var detector = new VisionWorkpieceDetector();
detector.Initialize("path/to/solution.sol", "password");

// 2. 配置检测参数
detector.ConfigureCircleDetection(edgeThreshold: 15, minRadius: 10, maxRadius: 200);
detector.ConfigureFeatureMatching(minScore: 0.7, maxMatchNum: 5);

// 3. 执行检测
var result = detector.DetectCircularWorkpiece("image.jpg");

// 4. 获取机器人坐标
string robotCoords = detector.ConvertToRobotCoordinates(result);
Console.WriteLine(robotCoords); // 输出: COORD:123.45,67.89,0.00

// 5. 释放资源
detector.Dispose();
```

### Python 版本使用

```python
# 1. 创建检测器实例
detector = VisionWorkpieceDetector("vision_config.json")

# 2. 加载图像
image = cv2.imread("workpiece.jpg")

# 3. 执行圆形检测
results = detector.detect_circular_workpiece(image)

# 4. 处理检测结果
for result in results:
    print(f"工件位置: ({result['center_x']}, {result['center_y']})")
    print(f"半径: {result['radius']}")
    print(f"置信度: {result['confidence']}")
    
    # 转换为机器人坐标
    robot_coords = detector.convert_to_robot_coordinates(result)
    print(f"机器人坐标: {robot_coords}")

# 5. 启动TCP服务器
detector.start_tcp_server(8888)
```

## 检测算法详解

### 1. 圆形工件检测

基于Hough圆变换的高精度圆形检测：

```python
# 配置圆形检测参数
circle_params = {
    'dp': 1,                # 累加器分辨率
    'min_dist': 50,         # 圆心最小距离
    'param1': 50,           # Canny边缘检测高阈值
    'param2': 30,           # 累加器阈值
    'min_radius': 10,       # 最小半径
    'max_radius': 200       # 最大半径
}
```

### 2. 模板匹配检测

支持多尺度、多角度的模板匹配：

```python
# 配置模板匹配参数
template_params = {
    'match_threshold': 0.7,     # 匹配阈值
    'scale_range': (0.8, 1.2),  # 缩放范围
    'angle_range': (-30, 30)    # 旋转角度范围
}
```

### 3. 特征点匹配

基于SIFT/ORB特征的快速匹配：

```python
# 特征匹配检测
results = detector.detect_feature_workpiece(image, template)
```

## 配置文件说明

`vision_config.json` 包含了系统的所有配置参数：

```json
{
  "communication": {
    "tcp_port": 8888,           # TCP服务器端口
    "robot_ip": "*************" # 机器人IP地址
  },
  "detection_algorithms": {
    "circle_detection": {
      "enabled": true,
      "confidence_threshold": 0.6
    }
  },
  "coordinate_system": {
    "pixel_to_mm_ratio": 0.1,   # 像素到毫米转换比例
    "origin_offset": {"x": 320, "y": 240}
  }
}
```

## 硬件连接协议 (基于plus.py)

系统集成了plus.py的硬件连接协议，可直接与DobotVisionStudio硬件通信：

### 连接配置

```json
{
  "hardware_connection": {
    "vision_hardware_ip": "*************",
    "vision_server_port": 6005,     // 接收硬件数据
    "vision_trigger_port": 6006,    // 发送触发命令
    "trigger_command": "TRIGGER"
  }
}
```

### 通信协议

**触发拍照**:
```
发送到端口6006: "TRIGGER"
```

**接收检测结果**:
```
从端口6005接收: "X,Y,R;image_path"
例如: "123.45,67.89,0.0;/path/to/captured_image.jpg"
```

## TCP通信协议

系统同时支持标准TCP通信，用于与机器人或上位机交互：

### 命令格式

```
# 触发检测
TRIGGER_VISION

# 坐标数据格式
COORD:123.45,67.89,0.00

# 图像数据格式
IMAGE:base64_encoded_image_data
```

### 通信示例

```python
import socket

# 连接到视觉系统
client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
client.connect(('*************', 8888))

# 发送触发命令
client.send(b'TRIGGER_VISION')

# 接收检测结果
response = client.recv(1024).decode('utf-8')
print(f"检测结果: {response}")

client.close()
```

## 性能优化

### 1. 检测速度优化

- 使用ROI区域限制检测范围
- 调整图像分辨率
- 启用GPU加速（如果支持）

### 2. 精度优化

- 相机标定和畸变校正
- 亚像素级边缘检测
- 多帧平均降噪

### 3. 稳定性优化

- 异常处理和错误恢复
- 内存管理和资源释放
- 多线程并发处理

## 扩展开发

### 添加自定义算法

```python
class CustomDetector(VisionWorkpieceDetector):
    def detect_custom_workpiece(self, image):
        # 实现自定义检测算法
        results = []
        # ... 算法实现 ...
        return results
```

### 集成深度学习

```python
# 集成YOLO或其他深度学习模型
import torch
from yolov5 import YOLOv5

model = YOLOv5('yolov5s.pt')
results = model(image)
```

## 故障排除

### 常见问题

1. **检测精度低**: 检查光照条件、相机标定、参数设置
2. **检测速度慢**: 优化ROI区域、降低图像分辨率、使用GPU
3. **TCP连接失败**: 检查网络配置、防火墙设置、端口占用

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 保存中间处理结果
detector.save_debug_images = True
```

## 技术支持

- 📧 技术支持: <EMAIL>
- 📖 详细文档: [DobotVisionStudio_视觉识别核心代码分析.md](DobotVisionStudio_视觉识别核心代码分析.md)
- 🐛 问题反馈: GitHub Issues

## 许可证

本项目基于MIT许可证开源，详见LICENSE文件。

## 更新日志

### v1.0.0 (2024-08-13)
- 初始版本发布
- 支持圆形检测、模板匹配、特征匹配
- 提供C#和Python双接口
- 集成TCP通信和机器人控制

---

**注意**: 本系统基于DobotVisionStudio4.1.2开发，使用前请确保已正确安装相关SDK和运行环境。
