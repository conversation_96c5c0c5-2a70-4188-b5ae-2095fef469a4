#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配合网络通讯助手测试自动化抓取功能
模拟视觉软件发送指令，测试完整的自动化流程
"""

import socket
import time
import threading
import json

class AutomationTester:
    """自动化测试器"""
    
    def __init__(self):
        # 视觉系统配置
        self.vision_ip = "************"
        self.detection1_port = 6005  # 检测区1端口
        self.detection2_port = 6006  # 检测区2端口
        
        # 服务器socket
        self.detection1_server = None
        self.detection2_server = None
        self.is_running = False
        
        # 测试数据
        self.test_material_data = {
            "material_1": {
                "id": "1",
                "x": 300.0,
                "y": 100.0,
                "r": 0.0,
                "description": "第一个物料点测试"
            }
        }
        
        print("🧪 自动化测试器初始化完成")
        print(f"📡 视觉系统IP: {self.vision_ip}")
        print(f"🔌 检测区1端口: {self.detection1_port}")
        print(f"🔌 检测区2端口: {self.detection2_port}")
    
    def start_vision_servers(self):
        """启动视觉系统模拟服务器"""
        try:
            # 启动检测区1服务器
            self.detection1_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.detection1_server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.detection1_server.bind((self.vision_ip, self.detection1_port))
            self.detection1_server.listen(5)
            
            # 启动检测区2服务器
            self.detection2_server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.detection2_server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.detection2_server.bind((self.vision_ip, self.detection2_port))
            self.detection2_server.listen(5)
            
            self.is_running = True
            
            print(f"✅ 检测区1服务器启动: {self.vision_ip}:{self.detection1_port}")
            print(f"✅ 检测区2服务器启动: {self.vision_ip}:{self.detection2_port}")
            
            # 启动服务器线程
            threading.Thread(target=self.handle_detection1_server, daemon=True).start()
            threading.Thread(target=self.handle_detection2_server, daemon=True).start()
            
            return True
            
        except Exception as e:
            print(f"❌ 启动视觉服务器失败: {e}")
            return False
    
    def handle_detection1_server(self):
        """处理检测区1服务器连接"""
        print("🔄 检测区1服务器开始监听...")
        
        while self.is_running:
            try:
                client_socket, addr = self.detection1_server.accept()
                print(f"📡 检测区1客户端连接: {addr}")
                
                # 处理客户端请求
                threading.Thread(
                    target=self.handle_detection1_client, 
                    args=(client_socket, addr), 
                    daemon=True
                ).start()
                
            except Exception as e:
                if self.is_running:
                    print(f"❌ 检测区1服务器错误: {e}")
    
    def handle_detection2_server(self):
        """处理检测区2服务器连接"""
        print("🔄 检测区2服务器开始监听...")
        
        while self.is_running:
            try:
                client_socket, addr = self.detection2_server.accept()
                print(f"📡 检测区2客户端连接: {addr}")
                
                # 处理客户端请求
                threading.Thread(
                    target=self.handle_detection2_client, 
                    args=(client_socket, addr), 
                    daemon=True
                ).start()
                
            except Exception as e:
                if self.is_running:
                    print(f"❌ 检测区2服务器错误: {e}")
    
    def handle_detection1_client(self, client_socket, addr):
        """处理检测区1客户端请求"""
        try:
            while self.is_running:
                data = client_socket.recv(1024).decode('utf-8').strip()
                if not data:
                    break
                
                print(f"📥 检测区1收到请求: {data}")
                
                # 模拟视觉检测响应
                if "trigger" in data.lower() or "detect" in data.lower():
                    # 返回第一个物料点的测试数据
                    material = self.test_material_data["material_1"]
                    response = f"{material['id']},{material['x']},{material['y']},{material['r']}"
                    
                    print(f"📤 检测区1响应: {response}")
                    client_socket.send(response.encode('utf-8'))
                    
                    print(f"✅ 检测区1检测完成 - 物料ID: {material['id']}, 坐标: ({material['x']}, {material['y']}, {material['r']})")
                else:
                    # 默认响应
                    response = "1,300.0,100.0,0.0"
                    client_socket.send(response.encode('utf-8'))
                
        except Exception as e:
            print(f"❌ 检测区1客户端处理错误: {e}")
        finally:
            client_socket.close()
            print(f"🔌 检测区1客户端 {addr} 断开连接")
    
    def handle_detection2_client(self, client_socket, addr):
        """处理检测区2客户端请求"""
        try:
            while self.is_running:
                data = client_socket.recv(1024).decode('utf-8').strip()
                if not data:
                    break
                
                print(f"📥 检测区2收到请求: {data}")
                
                # 模拟背面检测响应
                if "trigger" in data.lower() or "detect" in data.lower():
                    # 模拟OK结果（合格物料）
                    response = "OK"
                    
                    print(f"📤 检测区2响应: {response}")
                    client_socket.send(response.encode('utf-8'))
                    
                    print(f"✅ 检测区2检测完成 - 结果: {response}")
                else:
                    # 默认响应
                    response = "OK"
                    client_socket.send(response.encode('utf-8'))
                
        except Exception as e:
            print(f"❌ 检测区2客户端处理错误: {e}")
        finally:
            client_socket.close()
            print(f"🔌 检测区2客户端 {addr} 断开连接")
    
    def stop_servers(self):
        """停止服务器"""
        try:
            self.is_running = False
            
            if self.detection1_server:
                self.detection1_server.close()
            if self.detection2_server:
                self.detection2_server.close()
            
            print("✅ 视觉服务器已停止")
            
        except Exception as e:
            print(f"❌ 停止服务器时出错: {e}")
    
    def print_test_instructions(self):
        """打印测试说明"""
        print("\n" + "="*60)
        print("🧪 自动化抓取测试说明")
        print("="*60)
        print("1. 视觉系统模拟服务器已启动")
        print(f"   - 检测区1: {self.vision_ip}:{self.detection1_port}")
        print(f"   - 检测区2: {self.vision_ip}:{self.detection2_port}")
        print()
        print("2. 测试物料数据:")
        material = self.test_material_data["material_1"]
        print(f"   - 物料ID: {material['id']}")
        print(f"   - X坐标: {material['x']}")
        print(f"   - Y坐标: {material['y']}")
        print(f"   - R角度: {material['r']}")
        print(f"   - 描述: {material['description']}")
        print()
        print("3. 测试步骤:")
        print("   a) 启动 pro.py 主程序")
        print("   b) 连接机器人")
        print("   c) 点击'开始自动化'按钮")
        print("   d) 观察自动化流程执行")
        print()
        print("4. 预期流程:")
        print("   ① 机械臂移动到P7拍照位置")
        print("   ② 触发检测区1视觉检测")
        print("   ③ 接收物料坐标信息")
        print("   ④ 传送带移动到检测区1")
        print("   ⑤ 机械臂抓取物料")
        print("   ⑥ 传送带移动到检测区2")
        print("   ⑦ 触发检测区2背面检测")
        print("   ⑧ 根据检测结果放置物料")
        print("   ⑨ 返回初始位置")
        print()
        print("5. 网络通讯助手配置（可选）:")
        print("   - 如果要手动发送指令，请配置:")
        print(f"   - 服务器IP: {self.vision_ip}")
        print(f"   - 检测区1端口: {self.detection1_port}")
        print(f"   - 检测区2端口: {self.detection2_port}")
        print("   - 发送内容: 'trigger' 或任意文本")
        print("="*60)
    
    def run_test(self):
        """运行测试"""
        print("🚀 启动自动化抓取测试...")
        
        # 启动视觉服务器
        if not self.start_vision_servers():
            return False
        
        # 打印测试说明
        self.print_test_instructions()
        
        try:
            print("\n⏳ 测试服务器运行中... (按 Ctrl+C 停止)")
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断测试")
        finally:
            self.stop_servers()
        
        return True


def main():
    """主函数"""
    print("🧪 自动化抓取测试工具")
    print("配合网络通讯助手测试完整的自动化流程")
    print("-" * 50)
    
    tester = AutomationTester()
    
    print("\n选择测试模式:")
    print("1. 启动视觉模拟服务器（推荐）")
    print("2. 仅显示配置信息")
    print("0. 退出")
    
    try:
        choice = input("\n请选择 (0-2): ").strip()
        
        if choice == "0":
            print("👋 退出测试")
            return
        elif choice == "1":
            tester.run_test()
        elif choice == "2":
            tester.print_test_instructions()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")


if __name__ == "__main__":
    main()
