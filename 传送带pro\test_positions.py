#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证点位配置和自动化流程
"""

import json
import sys
import os

def test_position_config():
    """测试点位配置"""
    print("🧪 测试点位配置...")
    
    # 检查配置文件是否存在
    config_file = "robot_positions_config.json"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ 配置文件加载成功")
        print(f"📍 共有 {len(config['positions'])} 个点位")
        
        # 检查关键点位
        required_positions = [
            "P7", "P_Detection1", "P_Detection2", "P_Safe_Height",
            "P_Waste_Detection1", "P_Assembly1", "P_Material1_D2"
        ]
        
        missing_positions = []
        for pos in required_positions:
            if pos not in config['positions']:
                missing_positions.append(pos)
            else:
                joint_angles = config['positions'][pos]['joint_angles']
                print(f"   {pos}: {joint_angles}")
        
        if missing_positions:
            print(f"❌ 缺少关键点位: {missing_positions}")
            return False
        
        print("✅ 所有关键点位都已配置")
        
        # 检查传送带配置
        conveyor = config['conveyor_settings']
        print(f"🚚 传送带配置:")
        print(f"   检测区1位置: {conveyor['detection1_position']} mm")
        print(f"   检测区2位置: {conveyor['detection2_position']} mm")
        
        # 检查视觉配置
        vision = config['vision_settings']
        print(f"👁️ 视觉配置:")
        print(f"   IP地址: {vision['ip']}")
        print(f"   检测区1端口: {vision['detection1_port']}")
        print(f"   检测区2端口: {vision['detection2_port']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def compare_with_lua_config():
    """与lua配置进行对比"""
    print("\n🔍 与lua配置对比...")
    
    # lua代码中的配置
    lua_config = {
        "detection1_position": 258.45,
        "detection2_position": 643.34,
        "vision_ip": "************",
        "detection1_port": 6005,
        "detection2_port": 6006
    }
    
    try:
        with open("robot_positions_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        conveyor = config['conveyor_settings']
        vision = config['vision_settings']
        
        # 对比传送带配置
        if (conveyor['detection1_position'] == lua_config['detection1_position'] and
            conveyor['detection2_position'] == lua_config['detection2_position']):
            print("✅ 传送带位置配置与lua代码一致")
        else:
            print("❌ 传送带位置配置与lua代码不一致")
            return False
        
        # 对比视觉配置
        if (vision['ip'] == lua_config['vision_ip'] and
            vision['detection1_port'] == lua_config['detection1_port'] and
            vision['detection2_port'] == lua_config['detection2_port']):
            print("✅ 视觉系统配置与lua代码一致")
        else:
            print("❌ 视觉系统配置与lua代码不一致")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def test_pro_code_import():
    """测试pro.py代码导入"""
    print("\n🐍 测试pro.py代码导入...")
    
    try:
        # 尝试导入主要模块
        import customtkinter as ctk
        print("✅ customtkinter 导入成功")
        
        import socket
        print("✅ socket 导入成功")
        
        import threading
        print("✅ threading 导入成功")
        
        import queue
        print("✅ queue 导入成功")
        
        from PIL import Image, ImageTk
        print("✅ PIL 导入成功")
        
        from position_manager import position_manager
        print("✅ position_manager 导入成功")
        
        print("✅ 所有依赖模块导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保安装了所有必要的依赖包")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告...")
    
    report = {
        "test_time": "2025-08-14",
        "tests": {
            "position_config": test_position_config(),
            "lua_comparison": compare_with_lua_config(),
            "code_import": test_pro_code_import()
        }
    }
    
    # 保存报告
    with open("test_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 显示结果
    passed = sum(1 for result in report["tests"].values() if result)
    total = len(report["tests"])
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统配置正确。")
        print("\n💡 下一步操作建议:")
        print("1. 启动pro.py程序")
        print("2. 连接机器人")
        print("3. 初始化自动化系统")
        print("4. 测试各个点位移动")
        print("5. 启动自动化分拣流程")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
    
    return passed == total

if __name__ == "__main__":
    print("=" * 50)
    print("🤖 机器人分拣系统配置测试")
    print("=" * 50)
    
    success = generate_test_report()
    
    if success:
        print("\n✅ 测试完成，系统配置正确！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败，请检查配置！")
        sys.exit(1)
