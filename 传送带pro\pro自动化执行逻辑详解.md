# pro.py 自动化执行逻辑完整详解

## 🎯 **自动化系统总体架构**

```
用户点击"启动自动化" → 初始化自动化系统 → 启动主循环线程 → 执行分拣循环
```

## 📋 **完整执行流程**

### **阶段1: 自动化初始化** `initialize_automation_system()`

```python
🔧 正在初始化自动化系统...
├── 1. 应用配置参数
│   ├── 检测区1位置: 258.45 mm
│   ├── 检测区2位置: 643.34 mm
│   ├── 视觉系统IP: ************
│   └── 端口配置: 6005, 6006
├── 2. 初始化TCP连接到视觉软件
│   ├── 连接检测区1服务端 (IP:************, 端口:6005)
│   └── 连接检测区2服务端 (IP:************, 端口:6006)
├── 3. 初始化IO端口
│   ├── 关闭气泵: DO(2, 0)
│   └── 打开吸盘（准备状态）: DO(1, 1)
└── 4. 准备机械臂初始位置
    └── 尝试移动到P7位置（如果失败继续运行）
```

### **阶段2: 自动化主循环** `automation_main_loop()`

```python
while 自动化运行中:
    循环计数 += 1
    
    ========== 第 N 次分拣循环 ==========
    
    步骤1: 触发检测区1物料识别
    ├── 🤖 移动机械臂到拍照位置 P7
    │   ├── 尝试移动到 P7: MovJ({坐标})
    │   └── 如果失败，尝试 P_Camera_Pos
    ├── 📡 触发检测区1视觉检测
    │   ├── 发送检测指令到视觉系统
    │   └── 接收结果: material_id, pos_x, pos_y, pos_r
    └── 设置默认Z坐标: pos_z = 20.0
    
    步骤2: 控制传送带移动到检测区1
    ├── 🚚 计算移动距离
    ├── 选择移动方式:
    │   ├── DobotStudio Pro滑轨控制器 (优先)
    │   └── 传统MovJExt指令 (备用)
    └── 更新传送带位置显示
    
    步骤3-4: 执行基于lua逻辑的分拣流程
    └── 调用 pick_and_place_with_lua_logic()
    
    步骤5: 返回检测区1准备下一次循环
    ├── 🔄 移动到安全高度 P_Safe_Height
    ├── 🚚 移动传送带回到检测区1
    └── 🤖 移动机械臂到检测区1 P_Detection1
    
    等待2秒，开始下一次循环
```

### **阶段3: 分拣逻辑** `pick_and_place_with_lua_logic()`

```python
🔄 开始lua逻辑分拣流程 - 物料ID: {material_id}

步骤A: 抓取物料
└── enhanced_pick_material(x, y, z, r)
    ├── 🤖 移动到检测区1上方
    ├── 🤖 移动到物料位置上方 (x, y, z+安全高度)
    ├── 🤖 下降到抓取位置 (x, y, z)
    ├── 🔌 开启吸盘 DO(1, 1)
    ├── ⏱️ 等待吸附稳定
    ├── 🤖 抬升到安全高度
    └── 返回抓取结果

步骤B: 物料分类判断
├── 如果 material_id == "?"
│   └── ⚠️ 未识别物料 → 直接放置到废料点 P_Waste_Detection1
├── 如果 material_id 是数字 1-6
│   └── 进入两阶段检测流程
└── 其他情况
    └── ⚠️ 无效物料 → 放置到废料区 P_Assembly1

步骤C: 两阶段检测流程 (material_id = 1-6)
├── 🚚 移动传送带到检测区2
├── 🤖 移动机械臂到检测区2上方 P_Detection2
├── 📡 触发检测区2背面检测
├── 根据检测结果:
│   ├── 如果结果 == "OK"
│   │   └── ✅ 放置到装配区2对应槽位 P_Material{ID}_D2
│   └── 如果结果 == "NG" 或失败
│       ├── 🚚 返回检测区1
│       └── ⚠️ 放置到废料区 P_Assembly1
└── 🔄 抬高机械臂到安全高度
```

## 🎯 **关键点位和移动逻辑**

### **使用的关键点位**:
```python
# 拍照位置
"P7": [0.0, 32.29, 32.85, 0.0, 0.0, 0.0]  # 默认位置（最安全）
"P_Camera_Pos": [0.0, 32.29, 32.85, 0.0, 0.0, 0.0]  # 备用拍照位置

# 检测区位置
"P_Detection1": [1.9212, 69.2963, 66.6202, -0.3064, 0.0, 0.0]
"P_Detection2": [-3.0048, 23.4193, 23.358, 39.7952, 0.0, 0.0]

# 安全高度
"P_Safe_Height": [-2.3649, 29.5808, 31.8776, -27.4136, 0.0, 0.0]

# 废料位置
"P_Waste_Detection1": [24.9675, 70.9075, 66.9768, 39.7678, 0.0, 0.0]
"P_Assembly1": [0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0]  # 默认坐标

# 物料放置点（都使用默认坐标）
"P_Material1_D2": [-22.3874, 75.56, 76.7475, 74.3033, 0.0, 0.0]
"P_Material2_D2": [0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0]  # 默认坐标
"P_Material3_D2": [0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0]  # 默认坐标
"P_Material4_D2": [0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0]  # 默认坐标
"P_Material5_D2": [0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0]  # 默认坐标
"P_Material6_D2": [0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0]  # 默认坐标
```

### **移动指令**:
```python
# 统一使用关节角度移动
MovJ({J1, J2, J3, J4, J5, J6})

# 传送带移动
MovJExt(距离, {SYNC=1})  # 或使用滑轨控制器

# IO控制
DO(1, 1)  # 开启吸盘
DO(1, 0)  # 关闭吸盘
DO(2, 1)  # 开启气泵
DO(2, 0)  # 关闭气泵
```

## 🔄 **视觉检测通信**

### **检测区1通信** (端口6005):
```python
发送: "TRIGGER_DETECTION"
接收: "ID,X,Y,R" 或 "NO_OBJECT"
例如: "3,150.5,200.2,45.0"
```

### **检测区2通信** (端口6006):
```python
发送: "TRIGGER_DETECTION"
接收: "OK" 或 "NG"
```

## ⚠️ **错误处理机制**

### **连接错误**:
- Socket连接中断 → 尝试重连
- 视觉系统无响应 → 跳过本次循环

### **移动错误**:
- P7移动失败 → 尝试P_Camera_Pos
- 关节限位 → 使用安全坐标
- 传送带移动失败 → 跳过本次循环

### **抓取错误**:
- 抓取失败 → 记录错误，继续下一次循环
- 放置失败 → 尝试安全位置

## 🎯 **实际运行效果**

### **当前配置下的行为**:
1. **P7拍照**: 移动到默认位置 `[0.0, 32.29, 32.85, 0.0, 0.0, 0.0]`
2. **物料抓取**: 在检测区1抓取物料
3. **分类判断**: 根据视觉识别结果分类
4. **放置行为**:
   - ID=1的物料 → P_Material1_D2 (有效位置)
   - ID=2-6的物料 → 默认位置 `[0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0]`
   - 未识别物料 → 废料位置

### **循环特点**:
- 每次循环约2-5秒
- 自动错误恢复
- 连续运行直到手动停止
- 实时状态显示和日志记录

## 💡 **优化建议**

1. **如需真正分拣**: 重新标定P_Material2_D2到P_Material6_D2的实际位置
2. **提高效率**: 优化移动路径和等待时间
3. **增强稳定性**: 添加更多错误检查和恢复机制
4. **视觉优化**: 改进视觉检测的准确性和速度

**这就是pro.py中完整的自动化执行逻辑！** 🤖
