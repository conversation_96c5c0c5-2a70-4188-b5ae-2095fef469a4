#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试服务端 - 简单版本，用于验证连接
"""

import socket
import threading
import time

def start_simple_server(port, name):
    """启动简单的TCP服务端"""
    try:
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        # 绑定到所有可用的IP地址
        server_socket.bind(('0.0.0.0', port))
        server_socket.listen(1)
        
        print(f"🟢 {name} 服务端已启动，监听端口 {port}")
        print(f"   可以从任何IP地址连接到此端口")
        
        while True:
            try:
                client_socket, client_address = server_socket.accept()
                print(f"🤝 {name}: 客户端已连接 {client_address}")
                
                with client_socket:
                    while True:
                        try:
                            data = client_socket.recv(1024)
                            if not data:
                                break
                            
                            message = data.decode('utf-8').strip()
                            print(f"📥 {name} 收到: '{message}'")
                            
                            # 根据端口和指令返回相应的响应
                            if port == 6005:
                                if message.lower() == "ok":
                                    response = "1;320.53;-0.26;36.50"
                                else:
                                    response = "?;0;0;0"
                            elif port == 6006:
                                if message.lower() == "start":
                                    response = "OK"
                                else:
                                    response = "NG"
                            else:
                                response = f"Echo: {message}"
                            
                            print(f"📤 {name} 发送: '{response}'")
                            client_socket.sendall(response.encode('utf-8'))
                            
                        except Exception as e:
                            print(f"❌ {name} 处理数据时出错: {e}")
                            break
                            
                print(f"🔌 {name}: 客户端 {client_address} 已断开")
                
            except Exception as e:
                print(f"❌ {name} 服务端错误: {e}")
                break
                
    except Exception as e:
        print(f"❌ {name} 服务端启动失败: {e}")

def main():
    print("🚀 启动快速测试服务端")
    print("=" * 50)
    
    # 启动6005端口服务端
    thread_6005 = threading.Thread(target=start_simple_server, args=(6005, "检测区1(6005)"), daemon=True)
    thread_6005.start()
    
    # 启动6006端口服务端
    thread_6006 = threading.Thread(target=start_simple_server, args=(6006, "检测区2(6006)"), daemon=True)
    thread_6006.start()
    
    print("📋 测试说明:")
    print("1. 启动 pro.py 程序")
    print("2. 点击'连接诊断'检查连接状态")
    print("3. 点击'连接检测区1'和'连接检测区2'")
    print("4. 使用'发送到6005'和'发送到6006'测试通信")
    print("5. 按 Ctrl+C 停止服务端")
    print("=" * 50)
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务端...")
        print("✅ 服务端已停止")

if __name__ == "__main__":
    main()
