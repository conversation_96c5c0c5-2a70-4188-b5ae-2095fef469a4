# 机器人自动化分拣系统集成说明

## 概述

本项目成功将lua代码的自动化功能集成到Python的pro.py控制系统中，实现了不依赖DobotVisionPro的完整自动化分拣流程。

## 🎯 集成完成的功能

### 1. 位置管理系统 (position_manager.py)
- ✅ 位置记录和验证
- ✅ 位置数据持久化存储
- ✅ 位置精度验证
- ✅ 位置数据导出功能
- ✅ 容差设置和管理

### 2. 自动化控制面板
- ✅ 系统初始化控制
- ✅ 启动/停止自动化流程
- ✅ 实时状态显示
- ✅ 循环计数监控
- ✅ 参数配置界面
- ✅ 详细状态报告

### 3. TCP客户端通信功能 🆕
- ✅ **Python作为客户端** - 主动连接视觉软件服务端
- ✅ **检测区1通信** - 发送"ok"指令，接收物料ID和坐标
- ✅ **检测区2通信** - 发送"start"指令，接收OK/NG结果
- ✅ 实时TCP连接状态显示
- ✅ 数据解析功能 (支持分号和冒号格式)
- ✅ 自动重连和错误处理

### 4. 增强机械臂控制
- ✅ 安全移动函数 (safe_move_j, safe_move_l)
- ✅ 精确坐标移动
- ✅ 吸盘和喷气控制
- ✅ 增强抓取功能 (基于lua代码逻辑)
- ✅ 增强放置功能 (基于lua代码逻辑)
- ✅ 完整的抓取-检测-分拣流程

### 5. 完整自动化流程
- ✅ 两阶段检测流程
- ✅ 物料识别和分类
- ✅ 智能分拣逻辑
- ✅ 错误处理和恢复
- ✅ 循环执行控制

## 🚀 使用方法

### 启动系统
1. 运行 `python pro.py` 启动主控制界面
2. 连接机器人
3. 在"自动化分拣控制"面板中配置参数
4. 点击"初始化系统"
5. 点击"启动自动化"开始自动分拣

### 配置参数
- **检测区1位置**: 默认258.45mm (可调整)
- **检测区2位置**: 默认643.34mm (可调整)
- **视觉系统IP**: 默认************ (可调整)
- **通信端口**: 检测区1=6005, 检测区2=6006

### 监控状态
- 实时查看循环次数
- 监控系统运行状态
- 查看TCP连接状态
- 点击"显示详细状态"查看完整报告

## 📋 自动化流程说明

### 完整工作流程（基于lua代码逻辑）
1. **初始化阶段**
   - Python连接到视觉软件的6005和6006端口服务端
   - 初始化IO端口 (吸盘、喷气)
   - 移动机械臂到初始位置

2. **检测区1 - 物料识别**
   - 移动传送带到检测区1位置
   - 移动机械臂到拍照位置
   - **发送"ok"指令到6005端口** - 触发物料识别
   - **接收响应** - 物料ID和坐标信息
   - 支持格式: `ID;X;Y;角度` 或 `ID:X:Y:角度`

3. **分拣判断**
   - **未识别物料(ID="?")**: 直接放置到检测区1废料点
   - **物料芯片(ID=1-6)**: 进入两阶段检测流程

4. **检测区2 - 背面检测**（仅物料芯片）
   - 保持吸取状态，移动传送带到检测区2位置
   - 移动机械臂到检测区2上方
   - **发送"start"指令到6006端口** - 触发背面缺陷检测
   - **接收响应** - OK/NG结果

5. **智能分拣**
   - **背面检测OK**: 放置到装配区2对应ID槽位 → 抬高机械臂 → 返回检测区1
   - **背面检测NG**: 抬高机械臂 → 返回检测区1 → 放置到装配区1(废料区)

6. **循环准备**
   - 返回检测区1位置
   - 准备下一次循环

### 🔌 TCP通信协议（Python客户端模式）
- **检测区1 (6005端口)**:
  - Python发送: `"ok"`
  - 视觉软件返回: `物料ID;X坐标;Y坐标;角度`
  - 示例: `1;300.5;150.2;45.0` 或 `?;0;0;0`

- **检测区2 (6006端口)**:
  - Python发送: `"start"`
  - 视觉软件返回: `OK` 或 `NG`
  - 示例: `OK`, `NG`

## 🔧 技术特性

### 错误处理
- 网络连接自动重试
- 超时检测和恢复
- 安全停止机制
- 详细错误日志

### 数据解析
- 支持多种数据格式
- 容错性强
- 实时数据验证

### 安全机制
- 安全移动路径
- IO状态监控
- 紧急停止功能
- 状态一致性检查

## 📁 文件结构

```
传送带pro/
├── pro.py                 # 主控制程序 (已集成自动化功能)
├── position_manager.py    # 位置管理模块
├── global.lua            # 原lua全局函数库 (参考)
├── src0.lua              # 原lua主程序 (参考)
├── point.json            # 点位配置文件
├── README_集成说明.md     # 本说明文件
├── test_integration.py   # 集成测试脚本
└── test_dual_ports.py    # 双端口监听测试脚本
```

## 🎉 集成成果

通过本次集成，成功实现了：

1. **完全独立运行**: 不再依赖DobotVisionPro软件
2. **功能完整性**: 保留了lua代码的所有核心功能
3. **用户友好**: 提供了直观的图形界面控制
4. **稳定可靠**: 增加了错误处理和恢复机制
5. **易于维护**: 模块化设计，便于后续扩展

## 🧪 测试说明

### 视觉软件配置要求
您需要在视觉软件中配置两个TCP服务端：

#### 检测区1服务端 (端口6005)
- 监听端口：6005
- 接收指令：`"ok"`
- 返回格式：`ID;X;Y;角度`
- 示例返回：
  - `"1;300.5;150.2;45.0"` (物料芯片1)
  - `"?;0;0;0"` (未识别物料)

#### 检测区2服务端 (端口6006)
- 监听端口：6006
- 接收指令：`"start"`
- 返回格式：`OK` 或 `NG`

## 🔍 注意事项

1. **重要**: 视觉软件需要创建6005和6006端口的TCP服务端
2. Python程序作为客户端主动连接到视觉软件
3. 检查点位配置是否正确
4. 运行前先进行系统初始化
5. 监控日志输出以了解系统状态
6. 如遇问题可查看详细状态报告

## 📞 技术支持

如有问题，请检查：
1. 视觉软件TCP服务端状态 (6005, 6006)
2. Python客户端连接状态
3. 机器人连接状态
4. 指令发送和响应接收
5. 日志错误信息和数据格式

系统已完成lua逻辑集成，严格按照原lua代码的工作流程运行！🚀
