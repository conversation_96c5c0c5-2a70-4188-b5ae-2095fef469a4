#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证点位配置是否正确集成
"""

import json
import sys

def verify_pro_positions():
    """验证pro.py中的点位配置"""
    print("🔍 验证pro.py中的点位配置...")
    
    try:
        # 读取pro.py文件内容
        with open('pro.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键点位是否存在
        key_positions = [
            '"P7":', '"P_Camera_Pos":', '"P_Detection1":', '"P_Detection2":',
            '"P_Safe_Height":', '"P_Waste_Detection1":', '"P_Assembly1":',
            '"P_Material1_D2":', '"P_Material2_D2":', '"P_Material3_D2":',
            '"P_Material4_D2":', '"P_Material5_D2":', '"P_Material6_D2":'
        ]
        
        missing_positions = []
        found_positions = []
        
        for pos in key_positions:
            if pos in content:
                pos_name = pos.strip('":')
                found_positions.append(pos_name)
                print(f"✅ 找到点位: {pos_name}")
            else:
                pos_name = pos.strip('":')
                missing_positions.append(pos_name)
                print(f"❌ 缺少点位: {pos_name}")
        
        # 检查传送带配置
        conveyor_configs = [
            'self.detection1_position = 258.45',
            'self.detection2_position = 643.34'
        ]
        
        for config in conveyor_configs:
            if config in content:
                print(f"✅ 找到传送带配置: {config}")
            else:
                print(f"❌ 缺少传送带配置: {config}")
        
        # 检查视觉配置
        vision_configs = [
            'self.vision_ip = "************"',
            'self.detection1_port = 6005',
            'self.detection2_port = 6006'
        ]
        
        for config in vision_configs:
            if config in content:
                print(f"✅ 找到视觉配置: {config}")
            else:
                print(f"❌ 缺少视觉配置: {config}")
        
        print(f"\n📊 点位统计:")
        print(f"   找到点位: {len(found_positions)}")
        print(f"   缺少点位: {len(missing_positions)}")
        
        if missing_positions:
            print(f"\n⚠️ 缺少的点位: {missing_positions}")
            return False
        else:
            print(f"\n✅ 所有关键点位都已正确配置")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def compare_with_config():
    """与配置文件对比"""
    print("\n🔍 与配置文件对比...")
    
    try:
        # 读取配置文件
        with open('robot_positions_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 读取pro.py文件
        with open('pro.py', 'r', encoding='utf-8') as f:
            pro_content = f.read()
        
        # 检查关键点位的坐标是否一致
        key_positions = ["P7", "P_Detection1", "P_Detection2", "P_Safe_Height"]
        
        for pos in key_positions:
            if pos in config['positions']:
                expected_coords = config['positions'][pos]['joint_angles']
                
                # 在pro.py中查找这个点位的坐标
                import re
                pattern = f'"{pos}":\s*\[([^\]]+)\]'
                match = re.search(pattern, pro_content)
                
                if match:
                    coords_str = match.group(1)
                    try:
                        actual_coords = [float(x.strip()) for x in coords_str.split(',')]
                        
                        # 比较坐标（允许小的浮点误差）
                        coords_match = True
                        for i, (expected, actual) in enumerate(zip(expected_coords, actual_coords)):
                            if abs(expected - actual) > 0.001:
                                coords_match = False
                                break
                        
                        if coords_match:
                            print(f"✅ {pos}: 坐标一致")
                        else:
                            print(f"❌ {pos}: 坐标不一致")
                            print(f"   期望: {expected_coords}")
                            print(f"   实际: {actual_coords}")
                    except:
                        print(f"❌ {pos}: 坐标格式错误")
                else:
                    print(f"❌ {pos}: 在pro.py中未找到")
            else:
                print(f"❌ {pos}: 在配置文件中未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")
        return False

def test_syntax():
    """测试语法是否正确"""
    print("\n🔍 测试pro.py语法...")
    
    try:
        with open('pro.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, 'pro.py', 'exec')
        print("✅ 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   行号: {e.lineno}")
        print(f"   位置: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def generate_position_summary():
    """生成点位总结"""
    print("\n📋 生成点位总结...")
    
    try:
        # 读取配置文件
        with open('robot_positions_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        summary = {
            "verification_time": "2025-08-14",
            "total_positions": len(config['positions']),
            "key_positions": {},
            "conveyor_settings": config['conveyor_settings'],
            "vision_settings": config['vision_settings'],
            "status": "verified"
        }
        
        # 添加关键点位信息
        key_positions = ["P7", "P_Detection1", "P_Detection2", "P_Safe_Height", 
                        "P_Waste_Detection1", "P_Assembly1", "P_Material1_D2"]
        
        for pos in key_positions:
            if pos in config['positions']:
                summary["key_positions"][pos] = {
                    "description": config['positions'][pos]['description'],
                    "joint_angles": config['positions'][pos]['joint_angles']
                }
        
        # 保存总结
        with open('position_verification_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print("✅ 点位总结已保存到: position_verification_summary.json")
        
        # 显示关键信息
        print(f"\n📊 点位总结:")
        print(f"   总点位数: {summary['total_positions']}")
        print(f"   关键点位数: {len(summary['key_positions'])}")
        print(f"   检测区1位置: {summary['conveyor_settings']['detection1_position']} mm")
        print(f"   检测区2位置: {summary['conveyor_settings']['detection2_position']} mm")
        print(f"   视觉系统IP: {summary['vision_settings']['ip']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成总结失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 验证点位配置集成结果")
    print("=" * 60)
    
    all_passed = True
    
    # 1. 验证pro.py中的点位配置
    if not verify_pro_positions():
        all_passed = False
    
    # 2. 与配置文件对比
    if not compare_with_config():
        all_passed = False
    
    # 3. 测试语法
    if not test_syntax():
        all_passed = False
    
    # 4. 生成总结
    if not generate_position_summary():
        all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 验证完成！所有配置都正确集成。")
        print("\n💡 下一步建议:")
        print("1. 运行 python test_robot_picking.py 测试机械臂功能")
        print("2. 运行 python pro.py 启动主程序")
        print("3. 在主程序中连接机器人并测试点位移动")
        print("4. 测试自动化分拣流程")
        print("5. 验证与视觉系统的通信")
    else:
        print("❌ 验证失败！请检查配置问题。")
    
    print("=" * 60)
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
