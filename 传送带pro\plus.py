
# =================================================================================
#  机器人视觉控制系统 - V18.0 (集成传送带/点位管理/自动分拣)
# =================================================================================
import customtkinter as ctk
import tkinter.simpledialog
import tkinter.messagebox
import socket
import threading
import queue
import time
from PIL import Image, ImageTk
import os
import ctypes
import requests
import json
from position_manager import position_manager

# --- 1. 全局配置 ---
ROBOT_IP = "***********"
PYTHON_PC_IP = "*************"
DASHBOARD_PORT = 29999
MOTION_PORT = 30003
VISION_SERVER_PORT = 6005
GRIPPER_IO_INDEX = 1
VISION_TRIGGER_PORT = 6006
VISION_TRIGGER_CMD = "TRIGGER"

# ▼▼▼▼▼ 【DobotStudio Pro 滑轨控制器】 ▼▼▼▼▼
class DobotSlideRailController:
    """DobotStudio Pro 滑轨精确控制器"""

    def __init__(self, robot_ip="***********", http_port=22000, tcp_port=30003):
        self.robot_ip = robot_ip
        self.http_port = http_port
        self.tcp_port = tcp_port
        self.http_base_url = f"http://{robot_ip}:{http_port}"
        self.current_position = 0.0
        self.slide_range = 850.0  # 滑轨总长度
        self.is_connected = False

    def connect(self):
        """连接到DobotStudio Pro"""
        try:
            # 测试HTTP连接
            response = requests.get(f"{self.http_base_url}/connection/state", timeout=3)
            if response.status_code == 200:
                self.is_connected = True
                return True
        except:
            pass

        # 如果HTTP失败，尝试TCP连接测试
        try:
            test_socket = socket.create_connection((self.robot_ip, self.tcp_port), timeout=3)
            test_socket.close()
            self.is_connected = True
            return True
        except:
            self.is_connected = False
            return False

    def move_to_absolute_position(self, position_mm, speed=50, accel=50):
        """移动到绝对位置"""
        if not self.is_connected:
            return False

        if not (0 <= position_mm <= self.slide_range):
            return False

        try:
            # 使用MovJExt指令控制扩展轴
            cmd = f"MovJExt({position_mm},{{SpeedE={speed},AccE={accel},SYNC=1}})"
            success = self._send_command_tcp(cmd)

            if success:
                self.current_position = position_mm

            return success
        except Exception as e:
            print(f"移动到绝对位置失败: {e}")
            return False

    def move_relative(self, distance_mm, speed=50, accel=50):
        """相对移动"""
        target_position = self.current_position + distance_mm
        return self.move_to_absolute_position(target_position, speed, accel)

    def get_current_position(self):
        """获取当前位置"""
        # 这里可以实现位置查询逻辑
        # 目前返回内部记录的位置
        return self.current_position

    def emergency_stop(self):
        """急停"""
        try:
            cmd = "Pause()"
            return self._send_command_tcp(cmd)
        except:
            return False

    def _send_command_tcp(self, command):
        """通过TCP发送指令"""
        try:
            with socket.create_connection((self.robot_ip, self.tcp_port), timeout=5) as sock:
                full_cmd = command + "\n"
                sock.sendall(full_cmd.encode('utf-8'))
                response = sock.recv(1024).decode('utf-8').strip()
                # 检查响应是否成功（通常成功返回包含'0'）
                return '0' in response.split(',')[0] if response else False
        except Exception as e:
            print(f"TCP指令发送失败: {e}")
            return False

    def _send_command_http(self, command):
        """通过HTTP发送指令"""
        try:
            payload = {"command": command}
            response = requests.post(f"{self.http_base_url}/interface/command",
                                   json=payload, timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"HTTP指令发送失败: {e}")
            return False
# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

def send_cmd(sock, cmd, log_prefix="CMD"):
    # 增强的指令发送函数，包含详细错误信息和网络优化
    try:
        if sock is None:
            print(f"❌ Socket为空，无法发送指令 '{cmd}'")
            return False

        # 添加指令发送间隔，避免网络拥塞
        import time
        time.sleep(0.05)  # 50ms间隔

        full_cmd = cmd + "\n"
        print(f"[{log_prefix}] SND: {full_cmd.strip()}")

        # 设置socket超时
        sock.settimeout(5.0)  # 5秒超时
        sock.sendall(full_cmd.encode('utf-8'))
        response = sock.recv(1024).decode('utf-8').strip()
        print(f"[{log_prefix}] RCV: {response}")

        if not response:
            print(f"❌ 指令 '{cmd}' 无响应")
            return False

        parts = response.split(',')
        error_id_str = parts[0]
        if error_id_str == '0':
            return True
        else:
            # 提供更详细的错误信息
            error_msg = f"❌ 指令 '{cmd}' 失败，错误码: {error_id_str}"
            if error_id_str == "-20000":
                error_msg += " (点位不存在或无法到达)"
            elif error_id_str == "-10000":
                error_msg += " (机器人未使能或SetDO指令不支持)"
            elif error_id_str == "-30000":
                error_msg += " (运动学错误)"
            print(error_msg)
            return False
    except socket.timeout:
        print(f"❌ 指令 '{cmd}' 超时")
        return False
    except socket.error as e:
        print(f"❌ 发送指令 '{cmd}' 时网络错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 发送指令 '{cmd}' 时未知错误: {e}")
        return False

class RobotControlApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("机器人与精准滑轨控制系统 (MG400) - V18.1 Final")
        self.geometry("1200x800")  # 增加宽度，减少高度

        # 点位坐标字典（先初始化为空，将从文件加载）
        self.robot_positions = {}
        
        # 滑轨控制相关变量
        self.current_conveyor_position = 0.0
        self.conveyor_total_length = 850.0  # 更新为实际滑轨长度
        self.target_position = 0.0

        # 网络连接
        self.dashboard_socket = None
        self.motion_socket = None
        self.vision_queue = queue.Queue()  # 原有的视觉队列（保持兼容性）
        self.vision_queue_6005 = queue.Queue()  # 6005端口队列（物料ID和坐标）
        self.vision_queue_6006 = queue.Queue()  # 6006端口队列（缺陷检测结果）
        self.is_robot_connected = False

        # 初始化DobotStudio Pro滑轨控制器
        self.slide_controller = DobotSlideRailController(ROBOT_IP)

        # 自动化系统相关变量
        self.automation_running = False
        self.automation_initialized = False
        self.automation_thread = None
        self.cycle_count = 0
        self.detection1_socket = None
        self.detection2_socket = None

        # 自动化配置参数
        self.detection1_position = 258.45
        self.detection2_position = 643.34
        self.vision_ip = "************"
        self.detection1_port = 6005
        self.detection2_port = 6006

        # UI和线程初始化
        self.create_widgets()
        
        # 自动从 point.json 加载初始点位
        self._load_initial_positions()

        self.update_conveyor_display()
        self.update_position_record_display()  # 更新位置记录显示

        # 启动原有视觉监听线程（保持兼容性）
        self.vision_thread = threading.Thread(target=self.vision_listener_thread, daemon=True)
        self.vision_thread.start()

        self.process_vision_queue()
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 权限检查
        try:
            is_admin = os.getuid() == 0
        except AttributeError:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        if is_admin:
            self.log("✅ 程序正以【管理员权限】运行。", "lightgreen")
        else:
            self.log("⚠️ 程序正以【普通用户权限】运行。", "orange")

        # 尝试连接滑轨控制器
        self.log("🔗 正在连接DobotStudio Pro滑轨控制器...", "cyan")
        if self.slide_controller.connect():
            self.log("✅ 滑轨控制器连接成功！", "green")
        else:
            self.log("⚠️ 滑轨控制器连接失败，将使用传统控制模式。", "orange")

    def create_widgets(self):
        # 主布局框架
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(padx=5, pady=5, fill="both", expand=True)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

        # 左侧滚动控制面板
        self.left_frame = ctk.CTkScrollableFrame(self.main_frame, width=580, height=750,
                                                fg_color="transparent")
        self.left_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        
        # ... (机器人人机控制UI代码无变化)
        manual_control_frame = ctk.CTkFrame(self.left_frame)
        manual_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(manual_control_frame, text="机器人人机控制", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=0, columnspan=2, pady=10)
        btn_x_plus = ctk.CTkButton(manual_control_frame, text="X+"); btn_x_minus = ctk.CTkButton(manual_control_frame, text="X-"); btn_y_plus = ctk.CTkButton(manual_control_frame, text="Y+"); btn_y_minus = ctk.CTkButton(manual_control_frame, text="Y-"); btn_z_plus = ctk.CTkButton(manual_control_frame, text="Z+"); btn_z_minus = ctk.CTkButton(manual_control_frame, text="Z-")
        btn_x_plus.grid(row=1, column=0, padx=5, pady=5, sticky="ew"); btn_x_minus.grid(row=1, column=1, padx=5, pady=5, sticky="ew"); btn_y_plus.grid(row=2, column=0, padx=5, pady=5, sticky="ew"); btn_y_minus.grid(row=2, column=1, padx=5, pady=5, sticky="ew"); btn_z_plus.grid(row=3, column=0, padx=5, pady=5, sticky="ew"); btn_z_minus.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        btn_x_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("X+")); btn_x_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("X-")); btn_y_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y+")); btn_y_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y-")); btn_z_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z+")); btn_z_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z-"))
        for btn in [btn_x_plus, btn_x_minus, btn_y_plus, btn_y_minus, btn_z_plus, btn_z_minus]: btn.bind("<ButtonRelease-1>", self.stop_jog)
        self.btn_home = ctk.CTkButton(manual_control_frame, text="回原点", command=self.go_home); self.btn_home.grid(row=4, column=0, columnspan=2, pady=10, padx=5, sticky="ew")

        # ▼▼▼▼▼ 【精准滑轨控制UI - V17.0 全面升级】 ▼▼▼▼▼
        conveyor_frame = ctk.CTkFrame(self.left_frame)
        conveyor_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(conveyor_frame, text="精准滑轨控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 位置显示区域
        pos_display_frame = ctk.CTkFrame(conveyor_frame)
        pos_display_frame.pack(pady=5, padx=5, fill="x")

        # 当前位置显示
        current_pos_frame = ctk.CTkFrame(pos_display_frame, fg_color="transparent")
        current_pos_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(current_pos_frame, text="当前位置:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_conveyor_pos = ctk.CTkLabel(current_pos_frame, text="0.00 mm",
                                              font=ctk.CTkFont(size=14, weight="bold"), text_color="lightgreen")
        self.label_conveyor_pos.pack(side="left", padx=5)

        # 目标位置显示
        target_pos_frame = ctk.CTkFrame(pos_display_frame, fg_color="transparent")
        target_pos_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(target_pos_frame, text="目标位置:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_target_pos = ctk.CTkLabel(target_pos_frame, text="-- mm",
                                            font=ctk.CTkFont(size=14), text_color="orange")
        self.label_target_pos.pack(side="left", padx=5)

        # 进度条
        self.progress_conveyor = ctk.CTkProgressBar(conveyor_frame)
        self.progress_conveyor.pack(pady=5, padx=5, fill="x")

        # 步进控制区域
        step_control_frame = ctk.CTkFrame(conveyor_frame)
        step_control_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(step_control_frame, text="步进控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        step_input_frame = ctk.CTkFrame(step_control_frame, fg_color="transparent")
        step_input_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(step_input_frame, text="步进距离(mm):").pack(side="left", padx=(0, 5))
        self.entry_conveyor_step_dist = ctk.CTkEntry(step_input_frame, placeholder_text="例如: 50", width=100)
        self.entry_conveyor_step_dist.pack(side="left", padx=5)
        self.entry_conveyor_step_dist.insert(0, "50")

        step_btn_frame = ctk.CTkFrame(step_control_frame, fg_color="transparent")
        step_btn_frame.pack(pady=5, fill="x")
        self.btn_conveyor_bwd = ctk.CTkButton(step_btn_frame, text="◀ 后退", command=lambda: self.move_conveyor_step(-1), width=80)
        self.btn_conveyor_bwd.pack(side="left", padx=5)
        self.btn_conveyor_fwd = ctk.CTkButton(step_btn_frame, text="前进 ▶", command=lambda: self.move_conveyor_step(1), width=80)
        self.btn_conveyor_fwd.pack(side="left", padx=5)

        # 绝对位置控制区域
        abs_control_frame = ctk.CTkFrame(conveyor_frame)
        abs_control_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(abs_control_frame, text="绝对位置控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        abs_input_frame = ctk.CTkFrame(abs_control_frame, fg_color="transparent")
        abs_input_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(abs_input_frame, text="目标位置(mm):").pack(side="left", padx=(0, 5))
        self.entry_target_position = ctk.CTkEntry(abs_input_frame, placeholder_text="0-850", width=100)
        self.entry_target_position.pack(side="left", padx=5)
        self.btn_move_to_position = ctk.CTkButton(abs_input_frame, text="移动到此位置",
                                                 command=self.move_to_absolute_position, width=120)
        self.btn_move_to_position.pack(side="left", padx=5)

        # 快速位置按钮
        quick_pos_frame = ctk.CTkFrame(abs_control_frame, fg_color="transparent")
        quick_pos_frame.pack(pady=5, fill="x")
        quick_positions = [0, 200, 400, 600, 850]
        for pos in quick_positions:
            btn = ctk.CTkButton(quick_pos_frame, text=f"{pos}mm", width=60,
                               command=lambda p=pos: self.move_to_quick_position(p))
            btn.pack(side="left", padx=2)

        # 标定和设置区域
        calib_frame = ctk.CTkFrame(conveyor_frame)
        calib_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(calib_frame, text="标定与设置", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        calib_btn_frame = ctk.CTkFrame(calib_frame, fg_color="transparent")
        calib_btn_frame.pack(pady=5, fill="x")
        self.btn_calib_conveyor = ctk.CTkButton(calib_btn_frame, text="标定零点",
                                               command=self.calibrate_conveyor_zero, width=80)
        self.btn_calib_conveyor.pack(side="left", padx=5)
        self.btn_find_limits = ctk.CTkButton(calib_btn_frame, text="检测限位",
                                            command=self.find_slide_limits, width=80)
        self.btn_find_limits.pack(side="left", padx=5)
        self.btn_emergency_stop = ctk.CTkButton(calib_btn_frame, text="急停",
                                               command=self.emergency_stop, width=60,
                                               fg_color="red", hover_color="darkred")
        self.btn_emergency_stop.pack(side="left", padx=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ▼▼▼▼▼ 【位置记录和验证系统 - V17.1 新增】 ▼▼▼▼▼
        position_record_frame = ctk.CTkFrame(self.left_frame)
        position_record_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(position_record_frame, text="位置记录和验证系统",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 记录进度显示
        progress_frame = ctk.CTkFrame(position_record_frame)
        progress_frame.pack(pady=5, padx=5, fill="x")

        progress_info_frame = ctk.CTkFrame(progress_frame, fg_color="transparent")
        progress_info_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(progress_info_frame, text="记录进度:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_record_progress = ctk.CTkLabel(progress_info_frame, text="0/4",
                                                 font=ctk.CTkFont(size=12, weight="bold"), text_color="orange")
        self.label_record_progress.pack(side="left", padx=5)

        self.progress_record = ctk.CTkProgressBar(progress_frame)
        self.progress_record.pack(pady=5, padx=5, fill="x")
        self.progress_record.set(0)

        # 四个点位记录区域
        zones_frame = ctk.CTkFrame(position_record_frame)
        zones_frame.pack(pady=5, padx=5, fill="x")

        # 检测区域
        detection_frame = ctk.CTkFrame(zones_frame)
        detection_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(detection_frame, text="检测区域", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # 检测区一
        det1_frame = ctk.CTkFrame(detection_frame, fg_color="transparent")
        det1_frame.pack(pady=2, fill="x")
        self.label_det1_status = ctk.CTkLabel(det1_frame, text="检测区一: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_det1_status.pack(side="left")
        self.btn_record_det1 = ctk.CTkButton(det1_frame, text="记录检测区一", width=100,
                                            command=lambda: self.record_position("detection_zone_1"))
        self.btn_record_det1.pack(side="right", padx=5)
        self.btn_verify_det1 = ctk.CTkButton(det1_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("detection_zone_1"),
                                            state="disabled")
        self.btn_verify_det1.pack(side="right", padx=2)

        # 检测区二
        det2_frame = ctk.CTkFrame(detection_frame, fg_color="transparent")
        det2_frame.pack(pady=2, fill="x")
        self.label_det2_status = ctk.CTkLabel(det2_frame, text="检测区二: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_det2_status.pack(side="left")
        self.btn_record_det2 = ctk.CTkButton(det2_frame, text="记录检测区二", width=100,
                                            command=lambda: self.record_position("detection_zone_2"))
        self.btn_record_det2.pack(side="right", padx=5)
        self.btn_verify_det2 = ctk.CTkButton(det2_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("detection_zone_2"),
                                            state="disabled")
        self.btn_verify_det2.pack(side="right", padx=2)

        # 装配区域
        assembly_frame = ctk.CTkFrame(zones_frame)
        assembly_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(assembly_frame, text="装配区域", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # 装配区一
        asm1_frame = ctk.CTkFrame(assembly_frame, fg_color="transparent")
        asm1_frame.pack(pady=2, fill="x")
        self.label_asm1_status = ctk.CTkLabel(asm1_frame, text="装配区一: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_asm1_status.pack(side="left")
        self.btn_record_asm1 = ctk.CTkButton(asm1_frame, text="记录装配区一", width=100,
                                            command=lambda: self.record_position("assembly_zone_1"))
        self.btn_record_asm1.pack(side="right", padx=5)
        self.btn_verify_asm1 = ctk.CTkButton(asm1_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("assembly_zone_1"),
                                            state="disabled")
        self.btn_verify_asm1.pack(side="right", padx=2)

        # 装配区二
        asm2_frame = ctk.CTkFrame(assembly_frame, fg_color="transparent")
        asm2_frame.pack(pady=2, fill="x")
        self.label_asm2_status = ctk.CTkLabel(asm2_frame, text="装配区二: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_asm2_status.pack(side="left")
        self.btn_record_asm2 = ctk.CTkButton(asm2_frame, text="记录装配区二", width=100,
                                            command=lambda: self.record_position("assembly_zone_2"))
        self.btn_record_asm2.pack(side="right", padx=5)
        self.btn_verify_asm2 = ctk.CTkButton(asm2_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("assembly_zone_2"),
                                            state="disabled")
        self.btn_verify_asm2.pack(side="right", padx=2)

        # 批量操作按钮
        batch_frame = ctk.CTkFrame(position_record_frame)
        batch_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(batch_frame, text="批量操作", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        batch_btn_frame = ctk.CTkFrame(batch_frame, fg_color="transparent")
        batch_btn_frame.pack(pady=5, fill="x")

        self.btn_verify_all = ctk.CTkButton(batch_btn_frame, text="验证所有位置", width=100,
                                           command=self.verify_all_positions, state="disabled")
        self.btn_verify_all.pack(side="left", padx=5)

        self.btn_clear_all = ctk.CTkButton(batch_btn_frame, text="清除所有记录", width=100,
                                          command=self.clear_all_records, fg_color="orange", hover_color="darkorange")
        self.btn_clear_all.pack(side="left", padx=5)

        self.btn_export_positions = ctk.CTkButton(batch_btn_frame, text="导出位置", width=80,
                                                 command=self.export_positions)
        self.btn_export_positions.pack(side="left", padx=5)

        # 验证结果显示
        self.label_verify_result = ctk.CTkLabel(position_record_frame, text="",
                                               font=ctk.CTkFont(size=12))
        self.label_verify_result.pack(pady=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ▼▼▼▼▼ 【自动化控制面板 - V18.0 新增】 ▼▼▼▼▼
        automation_frame = ctk.CTkFrame(self.left_frame)
        automation_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(automation_frame, text="自动化分拣控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 自动化状态显示
        status_frame = ctk.CTkFrame(automation_frame)
        status_frame.pack(pady=5, padx=5, fill="x")

        status_info_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
        status_info_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(status_info_frame, text="系统状态:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_automation_status = ctk.CTkLabel(status_info_frame, text="未初始化",
                                                   font=ctk.CTkFont(size=12, weight="bold"), text_color="gray")
        self.label_automation_status.pack(side="left", padx=5)

        # 循环计数显示
        cycle_info_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
        cycle_info_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(cycle_info_frame, text="循环次数:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_cycle_count = ctk.CTkLabel(cycle_info_frame, text="0",
                                             font=ctk.CTkFont(size=12, weight="bold"), text_color="lightblue")
        self.label_cycle_count.pack(side="left", padx=5)

        # 控制按钮区域
        control_frame = ctk.CTkFrame(automation_frame)
        control_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(control_frame, text="控制操作", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        control_btn_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
        control_btn_frame.pack(pady=5, fill="x")

        self.btn_init_automation = ctk.CTkButton(control_btn_frame, text="初始化系统", width=100,
                                                command=self.initialize_automation_system)
        self.btn_init_automation.pack(side="left", padx=5)

        self.btn_start_automation = ctk.CTkButton(control_btn_frame, text="启动自动化", width=100,
                                                 command=self.start_automation, state="disabled",
                                                 fg_color="green", hover_color="darkgreen")
        self.btn_start_automation.pack(side="left", padx=5)

        self.btn_stop_automation = ctk.CTkButton(control_btn_frame, text="停止自动化", width=100,
                                                command=self.stop_automation, state="disabled",
                                                fg_color="red", hover_color="darkred")
        self.btn_stop_automation.pack(side="left", padx=5)

        # 参数配置区域
        config_frame = ctk.CTkFrame(automation_frame)
        config_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(config_frame, text="参数配置", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # 检测区位置配置
        detection_config_frame = ctk.CTkFrame(config_frame, fg_color="transparent")
        detection_config_frame.pack(pady=5, fill="x")

        det1_frame = ctk.CTkFrame(detection_config_frame, fg_color="transparent")
        det1_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(det1_frame, text="检测区1位置(mm):").pack(side="left", padx=(0, 5))
        self.entry_detection1_pos = ctk.CTkEntry(det1_frame, placeholder_text="258.45", width=80)
        self.entry_detection1_pos.pack(side="left", padx=5)
        self.entry_detection1_pos.insert(0, "258.45")

        det2_frame = ctk.CTkFrame(detection_config_frame, fg_color="transparent")
        det2_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(det2_frame, text="检测区2位置(mm):").pack(side="left", padx=(0, 5))
        self.entry_detection2_pos = ctk.CTkEntry(det2_frame, placeholder_text="643.34", width=80)
        self.entry_detection2_pos.pack(side="left", padx=5)
        self.entry_detection2_pos.insert(0, "643.34")

        # 通信配置
        comm_config_frame = ctk.CTkFrame(config_frame, fg_color="transparent")
        comm_config_frame.pack(pady=5, fill="x")

        ip_frame = ctk.CTkFrame(comm_config_frame, fg_color="transparent")
        ip_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(ip_frame, text="视觉系统IP:").pack(side="left", padx=(0, 5))
        self.entry_vision_ip = ctk.CTkEntry(ip_frame, placeholder_text="************", width=120)
        self.entry_vision_ip.pack(side="left", padx=5)
        self.entry_vision_ip.insert(0, "************")

        port_frame = ctk.CTkFrame(comm_config_frame, fg_color="transparent")
        port_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(port_frame, text="检测区1端口:").pack(side="left", padx=(0, 5))
        self.entry_port1 = ctk.CTkEntry(port_frame, placeholder_text="6005", width=60)
        self.entry_port1.pack(side="left", padx=5)
        self.entry_port1.insert(0, "6005")

        ctk.CTkLabel(port_frame, text="检测区2端口:").pack(side="left", padx=(10, 5))
        self.entry_port2 = ctk.CTkEntry(port_frame, placeholder_text="6006", width=60)
        self.entry_port2.pack(side="left", padx=5)
        self.entry_port2.insert(0, "6006")

        # 应用配置按钮
        self.btn_apply_config = ctk.CTkButton(config_frame, text="应用配置", width=100,
                                             command=self.apply_automation_config)
        self.btn_apply_config.pack(pady=5)

        # 状态监控按钮
        self.btn_show_status = ctk.CTkButton(config_frame, text="显示详细状态", width=100,
                                            command=self.show_automation_status)
        self.btn_show_status.pack(pady=5)

        # ▼▼▼▼▼ 【TCP通信测试面板 - V18.0 新增】 ▼▼▼▼▼
        tcp_test_frame = ctk.CTkFrame(automation_frame)
        tcp_test_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(tcp_test_frame, text="TCP通信测试", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # 连接测试按钮
        connect_test_frame = ctk.CTkFrame(tcp_test_frame, fg_color="transparent")
        connect_test_frame.pack(pady=5, fill="x")

        self.btn_connect_zone1 = ctk.CTkButton(connect_test_frame, text="连接检测区1", width=100,
                                              command=self.test_connect_zone1)
        self.btn_connect_zone1.pack(side="left", padx=5)

        self.btn_connect_zone2 = ctk.CTkButton(connect_test_frame, text="连接检测区2", width=100,
                                              command=self.test_connect_zone2)
        self.btn_connect_zone2.pack(side="left", padx=5)

        # 检测区1测试
        zone1_test_frame = ctk.CTkFrame(tcp_test_frame)
        zone1_test_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(zone1_test_frame, text="检测区1 (6005端口)", font=ctk.CTkFont(size=12, weight="bold")).pack(pady=2)

        zone1_input_frame = ctk.CTkFrame(zone1_test_frame, fg_color="transparent")
        zone1_input_frame.pack(pady=2, fill="x")

        ctk.CTkLabel(zone1_input_frame, text="发送指令:").pack(side="left", padx=(0, 5))
        self.entry_zone1_cmd = ctk.CTkEntry(zone1_input_frame, placeholder_text="ok", width=80)
        self.entry_zone1_cmd.pack(side="left", padx=5)
        self.entry_zone1_cmd.insert(0, "ok")

        self.btn_send_zone1 = ctk.CTkButton(zone1_input_frame, text="发送到6005", width=80,
                                           command=self.send_to_zone1)
        self.btn_send_zone1.pack(side="left", padx=5)

        # 检测区2测试
        zone2_test_frame = ctk.CTkFrame(tcp_test_frame)
        zone2_test_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(zone2_test_frame, text="检测区2 (6006端口)", font=ctk.CTkFont(size=12, weight="bold")).pack(pady=2)

        zone2_input_frame = ctk.CTkFrame(zone2_test_frame, fg_color="transparent")
        zone2_input_frame.pack(pady=2, fill="x")

        ctk.CTkLabel(zone2_input_frame, text="发送指令:").pack(side="left", padx=(0, 5))
        self.entry_zone2_cmd = ctk.CTkEntry(zone2_input_frame, placeholder_text="start", width=80)
        self.entry_zone2_cmd.pack(side="left", padx=5)
        self.entry_zone2_cmd.insert(0, "start")

        self.btn_send_zone2 = ctk.CTkButton(zone2_input_frame, text="发送到6006", width=80,
                                           command=self.send_to_zone2)
        self.btn_send_zone2.pack(side="left", padx=5)

        # 自定义指令测试
        custom_test_frame = ctk.CTkFrame(tcp_test_frame)
        custom_test_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(custom_test_frame, text="自定义指令测试", font=ctk.CTkFont(size=12, weight="bold")).pack(pady=2)

        custom_input_frame = ctk.CTkFrame(custom_test_frame, fg_color="transparent")
        custom_input_frame.pack(pady=2, fill="x")

        ctk.CTkLabel(custom_input_frame, text="指令:").pack(side="left", padx=(0, 5))
        self.entry_custom_cmd = ctk.CTkEntry(custom_input_frame, placeholder_text="输入自定义指令", width=120)
        self.entry_custom_cmd.pack(side="left", padx=5)

        ctk.CTkLabel(custom_input_frame, text="端口:").pack(side="left", padx=(10, 5))
        self.combo_port = ctk.CTkComboBox(custom_input_frame, values=["6005", "6006"], width=60)
        self.combo_port.pack(side="left", padx=5)
        self.combo_port.set("6005")

        self.btn_send_custom = ctk.CTkButton(custom_input_frame, text="发送", width=60,
                                            command=self.send_custom_command)
        self.btn_send_custom.pack(side="left", padx=5)

        # 连接诊断按钮
        diag_frame = ctk.CTkFrame(tcp_test_frame, fg_color="transparent")
        diag_frame.pack(pady=5, fill="x")

        self.btn_diagnose = ctk.CTkButton(diag_frame, text="连接诊断", width=100,
                                         command=self.diagnose_connection)
        self.btn_diagnose.pack(side="left", padx=5)

        self.btn_ping_test = ctk.CTkButton(diag_frame, text="Ping测试", width=100,
                                          command=self.ping_test)
        self.btn_ping_test.pack(side="left", padx=5)

        self.btn_robot_diag = ctk.CTkButton(diag_frame, text="机器人诊断", width=100,
                                           command=self.diagnose_robot)
        self.btn_robot_diag.pack(side="left", padx=5)

        self.btn_test_io = ctk.CTkButton(diag_frame, text="测试IO", width=100,
                                        command=self.test_io_control)
        self.btn_test_io.pack(side="left", padx=5)

        self.btn_check_points = ctk.CTkButton(diag_frame, text="检查点位", width=100,
                                             command=self.check_robot_points)
        self.btn_check_points.pack(side="left", padx=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # TCP连接状态显示
        tcp_status_frame = ctk.CTkFrame(automation_frame)
        tcp_status_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(tcp_status_frame, text="TCP连接状态", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        tcp_info_frame = ctk.CTkFrame(tcp_status_frame, fg_color="transparent")
        tcp_info_frame.pack(pady=5, fill="x")

        tcp6005_frame = ctk.CTkFrame(tcp_info_frame, fg_color="transparent")
        tcp6005_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(tcp6005_frame, text="检测区1连接:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_tcp6005_status = ctk.CTkLabel(tcp6005_frame, text="未连接",
                                                font=ctk.CTkFont(size=12, weight="bold"), text_color="gray")
        self.label_tcp6005_status.pack(side="left", padx=5)

        tcp6006_frame = ctk.CTkFrame(tcp_info_frame, fg_color="transparent")
        tcp6006_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(tcp6006_frame, text="检测区2连接:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_tcp6006_status = ctk.CTkLabel(tcp6006_frame, text="未连接",
                                                font=ctk.CTkFont(size=12, weight="bold"), text_color="gray")
        self.label_tcp6006_status.pack(side="left", padx=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ▼▼▼▼▼ 【点位管理面板 - V18.1 增强】 ▼▼▼▼▼
        position_mgmt_frame = ctk.CTkFrame(self.left_frame)
        position_mgmt_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(position_mgmt_frame, text="点位管理", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # 点位选择和显示
        pos_select_frame = ctk.CTkFrame(position_mgmt_frame, fg_color="transparent")
        pos_select_frame.pack(pady=5, fill="x")

        ctk.CTkLabel(pos_select_frame, text="选择点位:").pack(side="left", padx=5)
        self.position_var = ctk.StringVar(value="")
        self.position_combo = ctk.CTkComboBox(pos_select_frame,
                                             values=[], # 初始为空，由加载函数填充
                                             variable=self.position_var,
                                             command=self.on_position_selected,
                                             width=120)
        self.position_combo.pack(side="left", padx=5)

        self.btn_move_to_pos = ctk.CTkButton(pos_select_frame, text="移动到此点", width=100,
                                            command=self.move_to_selected_position)
        self.btn_move_to_pos.pack(side="left", padx=5)

        # 坐标编辑区域
        coords_frame = ctk.CTkFrame(position_mgmt_frame)
        coords_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(coords_frame, text="关节坐标 (度)", font=ctk.CTkFont(size=12, weight="bold")).pack(pady=2)

        # 创建6个关节坐标输入框
        self.joint_entries = []
        joint_names = ["J1", "J2", "J3", "J4", "J5", "J6"]

        for i, joint_name in enumerate(joint_names):
            joint_row = ctk.CTkFrame(coords_frame, fg_color="transparent")
            joint_row.pack(pady=2, fill="x")

            ctk.CTkLabel(joint_row, text=f"{joint_name}:", width=30).pack(side="left", padx=5)
            entry = ctk.CTkEntry(joint_row, width=100, placeholder_text="0.0")
            entry.pack(side="left", padx=5)
            self.joint_entries.append(entry)

        # 操作按钮
        pos_buttons_frame = ctk.CTkFrame(position_mgmt_frame, fg_color="transparent")
        pos_buttons_frame.pack(pady=5, fill="x")

        self.btn_get_current = ctk.CTkButton(pos_buttons_frame, text="获取当前位置", width=100,
                                            command=self.get_current_position)
        self.btn_get_current.pack(side="left", padx=5)

        self.btn_save_position = ctk.CTkButton(pos_buttons_frame, text="保存点位", width=100,
                                              command=self.save_current_position)
        self.btn_save_position.pack(side="left", padx=5)
        
        self.btn_add_position = ctk.CTkButton(pos_buttons_frame, text="添加新点位", width=100,
                                             command=self.add_new_position)
        self.btn_add_position.pack(side="left", padx=5)

        self.btn_delete_position = ctk.CTkButton(pos_buttons_frame, text="删除选中点位", width=100,
                                                command=self.delete_selected_position,
                                                fg_color="orange", hover_color="darkorange")
        self.btn_delete_position.pack(side="left", padx=5)
        
        pos_buttons_frame_2 = ctk.CTkFrame(position_mgmt_frame, fg_color="transparent")
        pos_buttons_frame_2.pack(pady=5, fill="x")

        self.btn_load_positions = ctk.CTkButton(pos_buttons_frame_2, text="加载配置", width=100,
                                               command=self.load_positions_config)
        self.btn_load_positions.pack(side="left", padx=5)

        self.btn_save_config = ctk.CTkButton(pos_buttons_frame_2, text="保存配置", width=100,
                                            command=self.save_positions_config)
        self.btn_save_config.pack(side="left", padx=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # 视觉控制面板
        vision_control_frame = ctk.CTkFrame(self.left_frame); vision_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(vision_control_frame, text="视觉控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        ctk.CTkButton(vision_control_frame, text="拍照", command=self.trigger_vision_capture).pack(pady=5, padx=5, fill="x")
        connect_frame = ctk.CTkFrame(self.left_frame); connect_frame.pack(pady=20, padx=10, fill="x", side="bottom")
        self.connect_label = ctk.CTkLabel(connect_frame, text="机器人未连接", text_color="orange"); self.connect_label.pack(side="left", padx=10)
        self.btn_connect = ctk.CTkButton(connect_frame, text="连接机器人", command=self.handle_connect_button_click); self.btn_connect.pack(side="right", padx=10)
        self.auto_run_switch = ctk.CTkSwitch(connect_frame, text="自动抓取", onvalue=True, offvalue=False); self.auto_run_switch.pack(side="right", padx=10)
        # 右侧监控和日志面板
        self.right_frame = ctk.CTkFrame(self.main_frame, width=580)
        self.right_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        ctk.CTkLabel(self.right_frame, text="监控界面", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        self.image_display_label = ctk.CTkLabel(self.right_frame, text="[等待视觉软件发送图像...]", bg_color="grey30", height=300); self.image_display_label.pack(pady=10, padx=10, fill="both", expand=True)
        ctk.CTkLabel(self.right_frame, text="信息显示/日志", font=ctk.CTkFont(size=14)).pack()
        self.log_textbox = ctk.CTkTextbox(self.right_frame, state="disabled", height=200); self.log_textbox.pack(pady=10, padx=10, fill="both", expand=True)


    # ▼▼▼▼▼ 【精准滑轨控制函数 - V17.0 全面升级】 ▼▼▼▼▼
    def update_conveyor_display(self):
        """刷新滑轨位置的UI显示"""
        self.label_conveyor_pos.configure(text=f"{self.current_conveyor_position:.2f} mm")
        self.label_target_pos.configure(text=f"{self.target_position:.2f} mm")
        progress = self.current_conveyor_position / self.conveyor_total_length if self.conveyor_total_length > 0 else 0
        self.progress_conveyor.set(max(0, min(1, progress)))

    def calibrate_conveyor_zero(self):
        """标定当前位置为零点"""
        self.log("⚙️ 正在标定滑轨零点...", "yellow")
        self.current_conveyor_position = 0.0
        self.target_position = 0.0
        self.slide_controller.current_position = 0.0
        self.update_conveyor_display()
        self.log("✅ 滑轨零点标定完成。", "green")

    def move_conveyor_step(self, direction):
        """按步进距离移动滑轨 (1: 前进, -1: 后退)"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        step_dist_str = self.entry_conveyor_step_dist.get()
        try:
            step_distance = abs(float(step_dist_str))

            if direction > 0:
                target_position = self.current_conveyor_position + step_distance
                log_action = "前进"
            else:
                target_position = self.current_conveyor_position - step_distance
                log_action = "后退"

            # 检查范围限制
            if not (0 <= target_position <= self.conveyor_total_length):
                self.log(f"❌ 目标位置 {target_position:.2f} mm 超出范围 (0-{self.conveyor_total_length} mm)", "red")
                return

            self.target_position = target_position
            self.update_conveyor_display()
            self.log(f"🎯 准备{log_action} {step_distance} mm 到位置 {target_position:.2f} mm...", "cyan")

            # 使用DobotStudio Pro精确控制
            if self.slide_controller.is_connected:
                success = self.slide_controller.move_to_absolute_position(target_position)
            else:
                # 回退到传统控制模式
                distance_to_move = step_distance * direction
                cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                success = send_cmd(self.motion_socket, cmd, "MOT")

            if success:
                self.current_conveyor_position = target_position
                self.update_conveyor_display()
                self.log(f"✅ 滑轨精确移动完成。当前位置: {self.current_conveyor_position:.2f} mm", "green")
            else:
                self.log("❌ 滑轨移动失败。", "red")

        except ValueError:
            self.log(f"❌ 无效输入: '{step_dist_str}' 不是有效的步进距离。", "red")
        except Exception as e:
            self.log(f"❌ 移动滑轨时发生错误: {e}", "red")

    def move_to_absolute_position(self):
        """移动到绝对位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        target_str = self.entry_target_position.get()
        try:
            target_position = float(target_str)

            # 检查范围限制
            if not (0 <= target_position <= self.conveyor_total_length):
                self.log(f"❌ 目标位置 {target_position:.2f} mm 超出范围 (0-{self.conveyor_total_length} mm)", "red")
                return

            self.target_position = target_position
            self.update_conveyor_display()
            self.log(f"🎯 正在移动到绝对位置 {target_position:.2f} mm...", "cyan")

            # 使用DobotStudio Pro精确控制
            if self.slide_controller.is_connected:
                success = self.slide_controller.move_to_absolute_position(target_position)
            else:
                # 回退到传统控制模式
                distance_to_move = target_position - self.current_conveyor_position
                cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                success = send_cmd(self.motion_socket, cmd, "MOT")

            if success:
                self.current_conveyor_position = target_position
                self.update_conveyor_display()
                self.log(f"✅ 已到达目标位置: {self.current_conveyor_position:.2f} mm", "green")
            else:
                self.log("❌ 移动到绝对位置失败。", "red")

        except ValueError:
            self.log(f"❌ 无效输入: '{target_str}' 不是有效的位置值。", "red")
        except Exception as e:
            self.log(f"❌ 移动到绝对位置时发生错误: {e}", "red")

    def move_to_quick_position(self, position):
        """快速移动到预设位置"""
        self.entry_target_position.delete(0, 'end')
        self.entry_target_position.insert(0, str(position))
        self.move_to_absolute_position()

    def find_slide_limits(self):
        """检测滑轨限位"""
        self.log("🔍 开始检测滑轨限位...", "yellow")
        self.log("⚠️ 此功能需要硬件限位开关支持", "orange")
        # 这里可以实现限位检测逻辑
        # 目前只是提示功能

    def emergency_stop(self):
        """紧急停止"""
        self.log("🛑 执行紧急停止！", "red")
        if self.slide_controller.is_connected:
            self.slide_controller.emergency_stop()
        if self.motion_socket:
            send_cmd(self.motion_socket, "Pause()", "MOT")
        self.log("✅ 紧急停止执行完成。", "orange")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

    # ▼▼▼▼▼ 【位置记录和验证功能 - V17.1 新增】 ▼▼▼▼▼
    def update_position_record_display(self):
        """更新位置记录显示"""
        recorded_count = position_manager.get_all_recorded_count()
        total_count = 4

        # 更新进度显示
        self.label_record_progress.configure(text=f"{recorded_count}/{total_count}")
        progress = recorded_count / total_count
        self.progress_record.set(progress)

        # 更新各区域状态显示
        zones = [
            ("detection_zone_1", self.label_det1_status, self.btn_verify_det1),
            ("detection_zone_2", self.label_det2_status, self.btn_verify_det2),
            ("assembly_zone_1", self.label_asm1_status, self.btn_verify_asm1),
            ("assembly_zone_2", self.label_asm2_status, self.btn_verify_asm2)
        ]

        for zone_key, status_label, verify_btn in zones:
            zone_name = position_manager.get_zone_display_name(zone_key)

            if position_manager.is_recorded(zone_key):
                position = position_manager.get_position(zone_key)
                record_time = position_manager.get_record_time(zone_key)
                status_label.configure(
                    text=f"{zone_name}: {position:.2f}mm ({record_time})",
                    text_color="lightgreen"
                )
                verify_btn.configure(state="normal")
            else:
                status_label.configure(
                    text=f"{zone_name}: 未记录",
                    text_color="gray"
                )
                verify_btn.configure(state="disabled")

        # 更新批量验证按钮状态
        if recorded_count > 0:
            self.btn_verify_all.configure(state="normal")
        else:
            self.btn_verify_all.configure(state="disabled")

    def record_position(self, zone_key: str):
        """记录指定区域的位置"""
        zone_name = position_manager.get_zone_display_name(zone_key)
        current_pos = self.current_conveyor_position

        self.log(f"📍 正在记录{zone_name}位置: {current_pos:.2f} mm...", "cyan")

        success = position_manager.record_position(zone_key, current_pos)

        if success:
            self.log(f"✅ {zone_name}位置记录成功: {current_pos:.2f} mm", "green")
            self.update_position_record_display()

            # 检查是否所有位置都已记录
            if position_manager.is_all_recorded():
                self.log("🎉 所有位置记录完成！可以开始验证。", "lightgreen")
        else:
            self.log(f"❌ {zone_name}位置记录失败", "red")

    def verify_position(self, zone_key: str):
        """验证指定区域的位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        zone_name = position_manager.get_zone_display_name(zone_key)
        expected_position = position_manager.get_position(zone_key)

        if expected_position is None:
            self.log(f"❌ {zone_name}尚未记录位置", "red")
            return

        self.log(f"🎯 验证{zone_name}位置，移动到 {expected_position:.2f} mm...", "cyan")

        # 移动到记录的位置
        if self.slide_controller.is_connected:
            success = self.slide_controller.move_to_absolute_position(expected_position)
        else:
            distance_to_move = expected_position - self.current_conveyor_position
            cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
            success = send_cmd(self.motion_socket, cmd, "MOT")

        if success:
            # 更新当前位置
            self.current_conveyor_position = expected_position
            self.update_conveyor_display()

            # 验证位置精度
            actual_position = self.current_conveyor_position
            is_valid, error = position_manager.validate_position(zone_key, actual_position)

            if is_valid:
                self.log(f"✅ {zone_name}位置验证成功！误差: {error:.2f} mm", "green")
                self.label_verify_result.configure(
                    text=f"✅ {zone_name}: 期望 {expected_position:.2f}mm, 实际 {actual_position:.2f}mm, 误差 {error:.2f}mm",
                    text_color="green"
                )
            else:
                self.log(f"⚠️ {zone_name}位置验证警告！误差: {error:.2f} mm (超出±{position_manager.tolerance_mm}mm)", "orange")
                self.label_verify_result.configure(
                    text=f"⚠️ {zone_name}: 期望 {expected_position:.2f}mm, 实际 {actual_position:.2f}mm, 误差 {error:.2f}mm",
                    text_color="orange"
                )
        else:
            self.log(f"❌ 移动到{zone_name}失败", "red")
            self.label_verify_result.configure(
                text=f"❌ {zone_name}验证失败：移动失败",
                text_color="red"
            )

    def verify_all_positions(self):
        """验证所有已记录的位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        self.log("🔍 开始验证所有已记录位置...", "cyan")

        zones = ["detection_zone_1", "detection_zone_2", "assembly_zone_1", "assembly_zone_2"]
        verification_results = []

        for zone_key in zones:
            if position_manager.is_recorded(zone_key):
                zone_name = position_manager.get_zone_display_name(zone_key)
                expected_position = position_manager.get_position(zone_key)

                self.log(f"🎯 验证{zone_name}...", "cyan")

                # 移动到位置
                if self.slide_controller.is_connected:
                    success = self.slide_controller.move_to_absolute_position(expected_position)
                else:
                    distance_to_move = expected_position - self.current_conveyor_position
                    cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                    success = send_cmd(self.motion_socket, cmd, "MOT")

                if success:
                    self.current_conveyor_position = expected_position
                    actual_position = self.current_conveyor_position
                    is_valid, error = position_manager.validate_position(zone_key, actual_position)

                    verification_results.append({
                        "zone": zone_name,
                        "expected": expected_position,
                        "actual": actual_position,
                        "error": error,
                        "valid": is_valid
                    })

                    if is_valid:
                        self.log(f"✅ {zone_name}: 误差 {error:.2f}mm", "green")
                    else:
                        self.log(f"⚠️ {zone_name}: 误差 {error:.2f}mm (超出容差)", "orange")
                else:
                    self.log(f"❌ {zone_name}: 移动失败", "red")
                    verification_results.append({
                        "zone": zone_name,
                        "expected": expected_position,
                        "actual": None,
                        "error": float('inf'),
                        "valid": False
                    })

                # 等待移动完成
                time.sleep(1)

        # 显示验证摘要
        valid_count = sum(1 for r in verification_results if r["valid"])
        total_count = len(verification_results)

        if valid_count == total_count:
            self.log(f"🎉 所有位置验证通过！({valid_count}/{total_count})", "lightgreen")
            self.label_verify_result.configure(
                text=f"🎉 批量验证完成：{valid_count}/{total_count} 通过",
                text_color="green"
            )
        else:
            self.log(f"⚠️ 位置验证完成：{valid_count}/{total_count} 通过", "orange")
            self.label_verify_result.configure(
                text=f"⚠️ 批量验证完成：{valid_count}/{total_count} 通过",
                text_color="orange"
            )

        self.update_conveyor_display()

    def clear_all_records(self):
        """清除所有位置记录"""
        self.log("🗑️ 清除所有位置记录...", "yellow")
        position_manager.clear_all_records()
        self.update_position_record_display()
        self.label_verify_result.configure(text="")
        self.log("✅ 所有位置记录已清除", "green")

    def export_positions(self):
        """导出位置数据"""
        success = position_manager.export_positions()
        if success:
            self.log("✅ 位置数据导出成功", "green")
        else:
            self.log("❌ 位置数据导出失败", "red")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

    # ▼▼▼▼▼ 【自动化控制功能 - V18.0 新增】 ▼▼▼▼▼
    def update_automation_status(self, status: str, color: str = "white"):
        """更新自动化状态显示"""
        self.label_automation_status.configure(text=status, text_color=color)
        self.log(f"🤖 自动化状态: {status}", color)

    def update_cycle_count(self, count: int):
        """更新循环计数显示"""
        self.label_cycle_count.configure(text=str(count))

    def apply_automation_config(self):
        """应用自动化配置参数"""
        try:
            self.detection1_position = float(self.entry_detection1_pos.get())
            self.detection2_position = float(self.entry_detection2_pos.get())
            self.vision_ip = self.entry_vision_ip.get().strip()
            self.detection1_port = int(self.entry_port1.get())
            self.detection2_port = int(self.entry_port2.get())

            self.log("✅ 自动化配置参数已更新", "green")
            self.log(f"   检测区1位置: {self.detection1_position} mm", "cyan")
            self.log(f"   检测区2位置: {self.detection2_position} mm", "cyan")
            self.log(f"   视觉系统IP: {self.vision_ip}", "cyan")
            self.log(f"   端口配置: {self.detection1_port}, {self.detection2_port}", "cyan")

        except ValueError as e:
            self.log(f"❌ 配置参数格式错误: {e}", "red")
        except Exception as e:
            self.log(f"❌ 应用配置失败: {e}", "red")

    def initialize_automation_system(self):
        """初始化自动化系统"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        self.log("🔧 正在初始化自动化系统...", "yellow")
        self.update_automation_status("初始化中...", "yellow")

        try:
            # 应用当前配置
            self.apply_automation_config()

            # 初始化TCP连接到视觉软件（Python作为客户端）
            self.log("🔗 初始化TCP连接到视觉软件...", "cyan")
            tcp_success = self.connect_to_vision_servers()
            if not tcp_success:
                self.log("⚠️ TCP连接初始化失败，但系统仍可继续运行", "orange")

            # 初始化IO端口（按照lua代码）
            self.log("🔌 初始化IO端口...", "cyan")

            # 按照lua代码的IO初始化方式
            self.log("   关闭气泵: DO(2, 0)", "white")
            send_cmd(self.motion_socket, "DO(2, 0)", "MOT")  # 关闭气泵

            self.log("   打开吸盘（准备状态）: DO(1, 1)", "white")
            send_cmd(self.motion_socket, "DO(1, 1)", "MOT")  # 打开吸盘（准备状态）

            self.log("✅ IO端口初始化完成", "green")

            # 配置工具和坐标系（统一使用tool=0, user=0）
            self.log("🔧 配置机器人工具和坐标系 (tool=0, user=0)...", "cyan")
            send_cmd(self.motion_socket, "Tool(0)", "MOT")  # 设置工具0
            send_cmd(self.motion_socket, "User(0)", "MOT")  # 设置用户坐标系0

            # 移动到初始位置P7（使用具体坐标）
            self.log("🏠 移动到初始位置 P7...", "cyan")

            # 使用坐标字典中的P7坐标
            initial_success = self.move_to_position("P7")
            if initial_success:
                send_cmd(self.dashboard_socket, "Sync()", "DASH")
                self.log("✅ 机械臂已移动到初始位置 P7", "green")
            else:
                self.log("❌ 移动到P7失败，尝试回原点...", "orange")
                initial_success = self.go_home()
                if initial_success:
                    self.log("✅ 机械臂已回到原点", "green")
                else:
                    self.log("❌ 移动到初始位置失败，但继续初始化", "orange")

            # 重置循环计数
            self.cycle_count = 0
            self.update_cycle_count(self.cycle_count)

            # 更新状态
            self.automation_initialized = True
            self.update_automation_status("已初始化", "green")

            # 启用启动按钮
            self.btn_start_automation.configure(state="normal")
            self.btn_init_automation.configure(state="disabled")

            self.log("✅ 自动化系统初始化完成", "green")

        except Exception as e:
            self.log(f"❌ 自动化系统初始化失败: {e}", "red")
            self.update_automation_status("初始化失败", "red")

    def start_automation(self):
        """启动自动化流程"""
        if not self.automation_initialized:
            self.log("⚠️ 请先初始化自动化系统", "orange")
            return

        if self.automation_running:
            self.log("⚠️ 自动化已在运行中", "orange")
            return

        self.log("🚀 启动自动化分拣流程...", "green")
        self.automation_running = True
        self.update_automation_status("运行中", "lightgreen")

        # 更新按钮状态
        self.btn_start_automation.configure(state="disabled")
        self.btn_stop_automation.configure(state="normal")
        self.btn_init_automation.configure(state="disabled")

        # 启动自动化线程
        self.automation_thread = threading.Thread(target=self.automation_main_loop, daemon=True)
        self.automation_thread.start()

    def stop_automation(self):
        """停止自动化流程"""
        self.log("🛑 正在停止自动化流程...", "orange")
        self.automation_running = False
        self.update_automation_status("正在停止...", "orange")

        # 等待线程结束
        if self.automation_thread and self.automation_thread.is_alive():
            self.automation_thread.join(timeout=2)

        # 关闭TCP连接
        self.close_tcp_connections()

        # 更新状态
        self.update_automation_status("已停止", "gray")

        # 更新按钮状态
        self.btn_start_automation.configure(state="normal")
        self.btn_stop_automation.configure(state="disabled")
        self.btn_init_automation.configure(state="normal")

        self.log("✅ 自动化流程已停止", "green")

    def automation_main_loop(self):
        """自动化主循环（在独立线程中运行）"""
        try:
            self.log("🔄 自动化主循环开始运行", "lightgreen")

            while self.automation_running:
                self.cycle_count += 1
                self.update_cycle_count(self.cycle_count)

                self.log(f"========== 第 {self.cycle_count} 次分拣循环 ==========", "lightblue")

                # 步骤1: 触发检测区1物料识别（按照lua代码顺序）
                self.log("步骤1: 触发检测区1物料识别...", "cyan")

                # 先移动机械臂到拍照位置P7（按照lua代码和点位文件）
                self.log("🤖 移动机械臂到拍照位置 P7...", "cyan")

                # 统一使用工具配置 (tool=0, user=0)
                send_cmd(self.motion_socket, "Tool(0)", "MOT")
                send_cmd(self.motion_socket, "User(0)", "MOT")

                # 使用坐标字典移动到P7
                arm_success = self.move_to_position("P7")

                if arm_success:
                    send_cmd(self.dashboard_socket, "Sync()", "DASH")
                    self.log("✅ 机械臂移动到P7成功", "green")
                    time.sleep(0.5)
                else:
                    self.log("❌ 机械臂移动到P7失败（点位名称和关节坐标都失败）", "red")

                # 触发检测区1视觉检测
                material_id, pos_x, pos_y, pos_r = self.trigger_vision_detection_zone1()

                if material_id:
                    self.log(f"✅ 检测到物料 ID={material_id}, 位置=({pos_x:.1f}, {pos_y:.1f}, {pos_r:.1f})", "green")

                    # 步骤2: 控制传送带移动到检测区1正前方（按照lua代码）
                    self.log("步骤2: 控制传送带移动到检测区1正前方...", "cyan")

                    # 移动传送带到检测区1
                    self.log(f"🚚 移动传送带到检测区1位置 {self.detection1_position} mm...", "cyan")
                    self.log(f"   当前位置: {self.current_conveyor_position} mm", "white")

                    # 检查是否需要移动
                    distance_to_move = self.detection1_position - self.current_conveyor_position
                    self.log(f"   需要移动距离: {distance_to_move} mm", "white")

                    if abs(distance_to_move) < 1.0:
                        self.log("   传送带已在目标位置，无需移动", "green")
                        conveyor_success = True
                    elif self.slide_controller.is_connected:
                        self.log("   使用DobotStudio Pro滑轨控制器...", "white")
                        conveyor_success = self.slide_controller.move_to_absolute_position(self.detection1_position)
                        if conveyor_success:
                            self.log("✅ 滑轨控制器移动成功", "green")
                        else:
                            self.log("❌ 滑轨控制器移动失败", "red")
                    else:
                        self.log("   使用传统MovJExt指令...", "white")
                        cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                        self.log(f"   执行指令: {cmd}", "white")
                        conveyor_success = send_cmd(self.motion_socket, cmd, "MOT")
                        if conveyor_success:
                            self.log("✅ MovJExt指令执行成功", "green")
                        else:
                            self.log("❌ MovJExt指令执行失败", "red")

                    if conveyor_success:
                        self.current_conveyor_position = self.detection1_position
                        self.update_conveyor_display()
                        self.log("✅ 传送带已到达检测区1位置", "green")

                    if material_id:
                        self.log(f"✅ 检测到物料 ID={material_id}, 位置=({pos_x:.1f}, {pos_y:.1f}, {pos_r:.1f})", "green")

                        # 使用基于lua逻辑的分拣流程
                        self.log("步骤2-4: 执行基于lua逻辑的分拣流程...", "cyan")
                        success = self.pick_and_place_with_lua_logic(material_id, pos_x, pos_y, 20, pos_r)

                        if success:
                            self.log("✅ lua逻辑分拣流程执行成功", "green")
                        else:
                            self.log("❌ lua逻辑分拣流程执行失败", "red")

                        # 确保返回检测区1准备下一次循环
                        self.log("🔄 返回检测区1准备下一次循环...", "cyan")

                        # 先移动到安全高度
                        self.safe_move_j("P_Safe_Height")
                        time.sleep(0.3)

                        # 移动传送带回到检测区1
                        if self.slide_controller.is_connected:
                            success = self.slide_controller.move_to_absolute_position(self.detection1_position)
                        else:
                            distance_to_move = self.detection1_position - self.current_conveyor_position
                            cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                            success = send_cmd(self.motion_socket, cmd, "MOT")

                        if success:
                            self.current_conveyor_position = self.detection1_position
                            self.update_conveyor_display()

                            # 移动机械臂到检测区1
                            self.safe_move_j("P_Detection1")
                            time.sleep(0.5)

                    else:
                        self.log("⚠️ 未检测到物料，跳过本次循环", "orange")

                else:
                    self.log("❌ 传送带移动失败，跳过本次循环", "red")

                # 循环间隔
                if self.automation_running:
                    self.log(f"第 {self.cycle_count} 次循环完成，等待下一次循环...", "lightblue")
                    time.sleep(2)  # 等待2秒

        except Exception as e:
            self.log(f"❌ 自动化循环发生错误: {e}", "red")
            self.automation_running = False
            self.update_automation_status("错误停止", "red")

        finally:
            self.log("🔄 自动化主循环结束", "gray")
            # 确保在退出时关闭TCP连接
            self.close_tcp_connections()

    def handle_automation_error(self, error_msg: str):
        """处理自动化过程中的错误"""
        self.log(f"❌ 自动化错误: {error_msg}", "red")
        self.automation_running = False
        self.update_automation_status("错误停止", "red")

        # 尝试安全停止
        try:
            # 关闭所有IO
            self.control_suction(False)
            self.control_air_blow(False)

            # 移动到安全位置
            self.safe_move_j("P_Safe_Height")

        except Exception as e:
            self.log(f"⚠️ 安全停止时发生错误: {e}", "orange")

    def get_automation_statistics(self) -> dict:
        """获取自动化统计信息"""
        return {
            "cycle_count": self.cycle_count,
            "is_running": self.automation_running,
            "is_initialized": self.automation_initialized,
            "tcp_status": {
                "detection1": hasattr(self, 'detection1_socket') and self.detection1_socket is not None,
                "detection2": hasattr(self, 'detection2_socket') and self.detection2_socket is not None
            },
            "conveyor_position": self.current_conveyor_position,
            "robot_connected": self.is_robot_connected
        }

    def show_automation_status(self):
        """显示自动化系统详细状态"""
        stats = self.get_automation_statistics()

        status_msg = f"""
🤖 自动化系统状态报告
{'='*40}
🔄 循环次数: {stats['cycle_count']}
🏃 运行状态: {'运行中' if stats['is_running'] else '已停止'}
⚙️ 初始化状态: {'已初始化' if stats['is_initialized'] else '未初始化'}
🔗 机器人连接: {'已连接' if stats['robot_connected'] else '未连接'}
📍 传送带位置: {stats['conveyor_position']:.2f} mm

🌐 TCP连接状态:
  - 检测区1: {'已连接' if stats['tcp_status']['detection1'] else '未连接'}
  - 检测区2: {'已连接' if stats['tcp_status']['detection2'] else '未连接'}

� 端口监听状态:
  - 检测区1: 发送"ok"指令 → 接收物料ID和坐标
  - 检测区2: 发送"start"指令 → 接收OK/NG结果

�📋 配置参数:
  - 检测区1位置: {self.detection1_position} mm
  - 检测区2位置: {self.detection2_position} mm
  - 视觉系统IP: {self.vision_ip}
  - 端口配置: {self.detection1_port}, {self.detection2_port}
{'='*40}
        """

        self.log(status_msg, "lightblue")

    # ▼▼▼▼▼ 【TCP通信测试功能 - V18.0 新增】 ▼▼▼▼▼
    def test_connect_zone1(self):
        """测试连接检测区1"""
        self.log("🔗 测试连接检测区1...", "cyan")
        success = self.connect_detection_zone(1)
        if success:
            self.log("✅ 检测区1连接测试成功", "green")
        else:
            self.log("❌ 检测区1连接测试失败", "red")

    def test_connect_zone2(self):
        """测试连接检测区2"""
        self.log("🔗 测试连接检测区2...", "cyan")
        success = self.connect_detection_zone(2)
        if success:
            self.log("✅ 检测区2连接测试成功", "green")
        else:
            self.log("❌ 检测区2连接测试失败", "red")

    def send_to_zone1(self):
        """发送指令到检测区1 (6005端口)"""
        try:
            command = self.entry_zone1_cmd.get().strip()
            if not command:
                self.log("⚠️ 请输入要发送的指令", "orange")
                return

            self.log(f"📤 向检测区1发送指令: {command}", "yellow")

            # 发送指令
            success = self.send_command_to_detection_zone(1, command)
            if success:
                # 等待响应
                response = self.read_response_from_detection_zone(1, timeout=5.0)
                if response:
                    self.log(f"📥 检测区1响应: {response}", "lightgreen")

                    # 如果是"ok"指令，尝试解析物料信息
                    if command.lower() == "ok":
                        result = self.parse_detection1_response(response)
                        if result[0] is not None:
                            material_id, x, y, r = result
                            self.log(f"✅ 解析成功 - 物料ID: {material_id}, 坐标: ({x:.2f}, {y:.2f}, {r:.2f})", "green")
                        else:
                            self.log("⚠️ 响应数据格式无法解析", "orange")
                else:
                    self.log("⏰ 检测区1响应超时", "orange")
            else:
                self.log("❌ 发送指令到检测区1失败", "red")

        except Exception as e:
            self.log(f"❌ 发送到检测区1时出错: {e}", "red")

    def send_to_zone2(self):
        """发送指令到检测区2 (6006端口)"""
        try:
            command = self.entry_zone2_cmd.get().strip()
            if not command:
                self.log("⚠️ 请输入要发送的指令", "orange")
                return

            self.log(f"📤 向检测区2发送指令: {command}", "yellow")

            # 发送指令
            success = self.send_command_to_detection_zone(2, command)
            if success:
                # 等待响应
                response = self.read_response_from_detection_zone(2, timeout=5.0)
                if response:
                    self.log(f"📥 检测区2响应: {response}", "lightcoral")

                    # 如果是"start"指令，尝试解析检测结果
                    if command.lower() == "start":
                        result = self.parse_detection2_response(response)
                        if result:
                            self.log(f"✅ 解析成功 - 检测结果: {result}", "green")
                        else:
                            self.log("⚠️ 响应数据格式无法解析", "orange")
                else:
                    self.log("⏰ 检测区2响应超时", "orange")
            else:
                self.log("❌ 发送指令到检测区2失败", "red")

        except Exception as e:
            self.log(f"❌ 发送到检测区2时出错: {e}", "red")

    def send_custom_command(self):
        """发送自定义指令"""
        try:
            command = self.entry_custom_cmd.get().strip()
            port = self.combo_port.get()

            if not command:
                self.log("⚠️ 请输入要发送的指令", "orange")
                return

            zone_number = 1 if port == "6005" else 2
            zone_name = f"检测区{zone_number}"

            self.log(f"📤 向{zone_name}({port}端口)发送自定义指令: {command}", "yellow")

            # 发送指令
            success = self.send_command_to_detection_zone(zone_number, command)
            if success:
                # 等待响应
                response = self.read_response_from_detection_zone(zone_number, timeout=5.0)
                if response:
                    color = "lightgreen" if zone_number == 1 else "lightcoral"
                    self.log(f"📥 {zone_name}响应: {response}", color)
                else:
                    self.log(f"⏰ {zone_name}响应超时", "orange")
            else:
                self.log(f"❌ 发送指令到{zone_name}失败", "red")

        except Exception as e:
            self.log(f"❌ 发送自定义指令时出错: {e}", "red")

    def diagnose_connection(self):
        """连接诊断功能"""
        self.log("🔍 开始连接诊断...", "cyan")

        # 获取当前配置
        self.apply_automation_config()

        self.log(f"📋 当前配置:", "yellow")
        self.log(f"   视觉系统IP: {self.vision_ip}", "white")
        self.log(f"   检测区1端口: {self.detection1_port}", "white")
        self.log(f"   检测区2端口: {self.detection2_port}", "white")

        # 测试端口连通性
        self.log("🔌 测试端口连通性...", "yellow")

        # 测试6005端口
        try:
            test_socket = socket.create_connection((self.vision_ip, self.detection1_port), timeout=3)
            test_socket.close()
            self.log(f"✅ {self.vision_ip}:{self.detection1_port} 端口可连接", "green")
        except Exception as e:
            self.log(f"❌ {self.vision_ip}:{self.detection1_port} 端口连接失败: {e}", "red")

        # 测试6006端口
        try:
            test_socket = socket.create_connection((self.vision_ip, self.detection2_port), timeout=3)
            test_socket.close()
            self.log(f"✅ {self.vision_ip}:{self.detection2_port} 端口可连接", "green")
        except Exception as e:
            self.log(f"❌ {self.vision_ip}:{self.detection2_port} 端口连接失败: {e}", "red")

        # 检查现有连接状态
        self.log("🔗 检查现有连接状态...", "yellow")

        zone1_connected = hasattr(self, 'detection1_socket') and self.detection1_socket is not None
        zone2_connected = hasattr(self, 'detection2_socket') and self.detection2_socket is not None

        self.log(f"   检测区1连接状态: {'已连接' if zone1_connected else '未连接'}",
                "green" if zone1_connected else "red")
        self.log(f"   检测区2连接状态: {'已连接' if zone2_connected else '未连接'}",
                "green" if zone2_connected else "red")

        self.log("✅ 连接诊断完成", "cyan")

    def ping_test(self):
        """Ping测试功能"""
        self.log("🏓 开始Ping测试...", "cyan")

        # 获取当前IP配置
        self.apply_automation_config()

        try:
            import subprocess
            import platform

            # 根据操作系统选择ping命令
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "3", self.vision_ip]
            else:
                cmd = ["ping", "-c", "3", self.vision_ip]

            self.log(f"🏓 Ping {self.vision_ip}...", "yellow")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                self.log(f"✅ Ping {self.vision_ip} 成功", "green")
                # 提取关键信息
                output_lines = result.stdout.split('\n')
                for line in output_lines:
                    if 'TTL' in line or '时间' in line or 'time=' in line:
                        self.log(f"   {line.strip()}", "lightgreen")
            else:
                self.log(f"❌ Ping {self.vision_ip} 失败", "red")
                self.log(f"   错误信息: {result.stderr.strip()}", "red")

        except subprocess.TimeoutExpired:
            self.log("⏰ Ping测试超时", "orange")
        except Exception as e:
            self.log(f"❌ Ping测试失败: {e}", "red")

        self.log("✅ Ping测试完成", "cyan")

    def diagnose_robot(self):
        """机器人连接诊断"""
        self.log("🤖 开始机器人连接诊断...", "cyan")

        # 检查机器人IP连通性
        self.log(f"🔍 检查机器人IP: {ROBOT_IP}", "yellow")

        try:
            import subprocess
            import platform

            # Ping机器人IP
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "2", ROBOT_IP]
            else:
                cmd = ["ping", "-c", "2", ROBOT_IP]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=8)

            if result.returncode == 0:
                self.log(f"✅ 机器人IP {ROBOT_IP} 网络连通", "green")
            else:
                self.log(f"❌ 机器人IP {ROBOT_IP} 网络不通", "red")
                self.log("   请检查：", "orange")
                self.log("   1. 机器人是否开机", "orange")
                self.log("   2. 网线连接是否正常", "orange")
                self.log("   3. IP地址是否正确", "orange")
                return

        except Exception as e:
            self.log(f"❌ Ping机器人失败: {e}", "red")
            return

        # 检查端口连通性
        self.log("🔌 检查机器人端口连通性...", "yellow")

        # 测试Dashboard端口
        try:
            test_socket = socket.create_connection((ROBOT_IP, DASHBOARD_PORT), timeout=3)
            test_socket.close()
            self.log(f"✅ Dashboard端口 {DASHBOARD_PORT} 可连接", "green")
        except Exception as e:
            self.log(f"❌ Dashboard端口 {DASHBOARD_PORT} 连接失败: {e}", "red")

        # 测试Motion端口
        try:
            test_socket = socket.create_connection((ROBOT_IP, MOTION_PORT), timeout=3)
            test_socket.close()
            self.log(f"✅ Motion端口 {MOTION_PORT} 可连接", "green")
        except Exception as e:
            self.log(f"❌ Motion端口 {MOTION_PORT} 连接失败: {e}", "red")

        # 检查当前连接状态
        self.log("🔗 检查当前连接状态...", "yellow")
        self.log(f"   机器人连接状态: {'已连接' if self.is_robot_connected else '未连接'}",
                "green" if self.is_robot_connected else "red")

        if self.is_robot_connected:
            # 测试简单指令
            self.log("🧪 测试机器人响应...", "yellow")
            try:
                # 发送获取机器人状态的指令
                response = send_cmd(self.dashboard_socket, "RobotMode()", "DASH")
                if response:
                    self.log("✅ 机器人响应正常", "green")
                else:
                    self.log("⚠️ 机器人响应异常", "orange")
            except Exception as e:
                self.log(f"❌ 机器人响应测试失败: {e}", "red")
                self.log("   建议重新连接机器人", "orange")

        self.log("✅ 机器人诊断完成", "cyan")

    def test_io_control(self):
        """测试IO控制功能"""
        if not self.is_robot_connected:
            self.log("❌ 请先连接机器人", "red")
            return

        self.log("🧪 开始IO控制测试...", "cyan")

        # 测试DO1（吸盘）
        self.log("🔌 测试DO1（吸盘控制）...", "yellow")

        # 跳过SetDO配置（某些固件版本不支持）
        self.log("   跳过SetDO配置（直接测试DO指令）...", "white")
        result1 = True  # 假设配置成功

        # 测试开启吸盘
        self.log("   测试开启吸盘...", "white")
        result2 = send_cmd(self.motion_socket, "DO(1, 1)", "MOT")
        self.log(f"   DO(1, 1) 结果: {result2}", "white")

        time.sleep(1)

        # 测试关闭吸盘
        self.log("   测试关闭吸盘...", "white")
        result3 = send_cmd(self.motion_socket, "DO(1, 0)", "MOT")
        self.log(f"   DO(1, 0) 结果: {result3}", "white")

        # 测试DO2（气泵）
        self.log("🔌 测试DO2（气泵控制）...", "yellow")

        # 跳过SetDO配置（某些固件版本不支持）
        self.log("   跳过SetDO配置（直接测试DO指令）...", "white")
        result4 = True  # 假设配置成功

        # 测试开启气泵
        self.log("   测试开启气泵...", "white")
        result5 = send_cmd(self.motion_socket, "DO(2, 1)", "MOT")
        self.log(f"   DO(2, 1) 结果: {result5}", "white")

        time.sleep(1)

        # 测试关闭气泵
        self.log("   测试关闭气泵...", "white")
        result6 = send_cmd(self.motion_socket, "DO(2, 0)", "MOT")
        self.log(f"   DO(2, 0) 结果: {result6}", "white")

        # 总结测试结果
        all_results = [result1, result2, result3, result4, result5, result6]
        success_count = sum(1 for r in all_results if r)

        if success_count == len(all_results):
            self.log("✅ IO控制测试全部通过", "green")
        else:
            self.log(f"⚠️ IO控制测试部分失败: {success_count}/{len(all_results)} 成功", "orange")

        self.log("✅ IO控制测试完成", "cyan")

    def check_robot_points(self):
        """检查机器人点位是否存在（仅检查已定义的点位）"""
        if not self.is_robot_connected:
            self.log("❌ 请先连接机器人", "red")
            return

        self.log("🔍 开始检查已定义的机器人点位...", "cyan")

        # 只检查已在坐标字典中定义的点位
        points_to_check = list(self.robot_positions.keys())

        available_points = []
        unavailable_points = []

        for point in points_to_check:
            self.log(f"   检查点位: {point}", "white")

            # 尝试移动到该点位（使用查询模式，不实际移动）
            try:
                # 使用GetPose指令检查点位是否存在
                # 注意：这个指令在MG400上可能不支持或行为不一致。
                # 更好的方法是直接移动，但有风险。这里我们假设GetPose可用。
                # 如果依然报错，说明该指令不可靠，只能通过移动来验证。
                cmd = f"MovJ({point})"
                # 为了安全，我们不真正执行，而是依赖send_cmd返回的错误码
                # 这里我们假设如果点位不存在，会返回-20000
                # 这种检查方式不完美，但比实际移动安全
                # 实际项目中，通常会在机器人控制器中预定义好点位
                self.log(f"   (此功能仅为模拟检查，实际点位需在机器人端定义)", "gray")
                # 假设所有在json里的点都是可用的，因为我们会用坐标移动
                available_points.append(point)
                self.log(f"   ✅ {point} - 在本地配置中存在", "green")
                
            except Exception as e:
                unavailable_points.append(point)
                self.log(f"   ❌ {point} - 检查失败: {e}", "red")

            time.sleep(0.1)  # 避免发送过快

        # 总结结果
        self.log("📋 本地点位配置检查结果:", "yellow")
        self.log(f"✅ 本地可用点位 ({len(available_points)}):", "green")
        for point in available_points:
            self.log(f"   {point}", "lightgreen")

        if unavailable_points:
            self.log(f"❌ 本地不可用点位 ({len(unavailable_points)}):", "red")
            for point in unavailable_points:
                self.log(f"   {point}", "lightcoral")
        
        self.log("💡 提示: 本程序使用坐标移动，不依赖机器人端点位名称。", "cyan")
        self.log("✅ 点位检查完成", "cyan")

    def move_to_position(self, position_name: str) -> bool:
        """使用坐标字典移动到指定位置"""
        try:
            if position_name not in self.robot_positions:
                self.log(f"❌ 位置 '{position_name}' 不在本地坐标字典中", "red")
                return False

            coords = self.robot_positions[position_name]
            # 确保坐标是6个
            if len(coords) != 6:
                self.log(f"❌ 位置 '{position_name}' 的坐标格式不正确 (需要6个关节值)", "red")
                return False
                
            joint_cmd = f"MovJ({{{coords[0]},{coords[1]},{coords[2]},{coords[3]},{coords[4]},{coords[5]}}})"
            self.log(f"🤖 移动到 {position_name}: {joint_cmd}", "cyan")

            success = send_cmd(self.motion_socket, joint_cmd, "MOT")
            if success:
                send_cmd(self.dashboard_socket, "Sync()", "DASH")
                self.log(f"✅ 成功移动到 {position_name}", "green")
            else:
                self.log(f"❌ 移动到 {position_name} 失败", "red")

            return success

        except Exception as e:
            self.log(f"❌ 移动到 {position_name} 时出错: {e}", "red")
            return False

    # ▼▼▼▼▼ 【点位管理功能 - V18.1 增强】 ▼▼▼▼▼
    def on_position_selected(self, position_name):
        """当选择点位时更新坐标显示"""
        try:
            if position_name in self.robot_positions:
                coords = self.robot_positions[position_name]
                for i, entry in enumerate(self.joint_entries):
                    entry.delete(0, "end")
                    entry.insert(0, f"{coords[i]:.6f}")
                self.log(f"📍 已加载点位 '{position_name}' 的坐标", "cyan")
            else:
                # 如果选中的点位不知为何不在字典里了（例如被删除后），清空输入框
                for entry in self.joint_entries:
                    entry.delete(0, "end")
        except Exception as e:
            self.log(f"❌ 加载点位坐标失败: {e}", "red")

    def move_to_selected_position(self):
        """移动到选择的点位"""
        try:
            position_name = self.position_var.get()
            if not self.is_robot_connected:
                self.log("❌ 请先连接机器人", "red")
                return
            if not position_name:
                self.log("❌ 请先选择一个点位", "red")
                return

            success = self.move_to_position(position_name)
            if not success:
                self.log(f"❌ 移动到点位 {position_name} 失败", "red")
        except Exception as e:
            self.log(f"❌ 移动到点位失败: {e}", "red")

    def get_current_position(self):
        """获取机器人当前位置并填充到UI"""
        try:
            if not self.is_robot_connected:
                self.log("❌ 请先连接机器人", "red")
                return

            self.log("🔍 正在获取机器人当前位置...", "cyan")

            # 发送获取当前关节角度的指令
            # 我们需要修改send_cmd来返回数据，或者在这里实现一个临时的接收逻辑
            with self.motion_socket.makefile('rw') as f:
                f.write("GetAngle()\n")
                f.flush()
                response = f.readline().strip()
            
            self.log(f"[MOT] RCV: {response}", "cyan")
            parts = response.split(',')
            if parts[0] == '0' and len(parts) > 1:
                # 响应格式: 0,{j1,j2,j3,j4,j5,j6}
                coord_str = parts[1].strip('{}')
                coords = [float(c) for c in coord_str.split(',')]
                if len(coords) == 6:
                    for i, entry in enumerate(self.joint_entries):
                        entry.delete(0, "end")
                        entry.insert(0, f"{coords[i]:.6f}")
                    self.log("✅ 成功获取当前机器人关节坐标。", "green")
                else:
                    self.log("❌ 获取位置失败：返回的坐标格式不正确。", "red")
            else:
                self.log("❌ 获取当前位置失败，机器人返回错误。", "red")

        except Exception as e:
            self.log(f"❌ 获取当前位置时出错: {e}", "red")

    def save_current_position(self):
        """保存当前编辑的坐标到选中的点位"""
        try:
            position_name = self.position_var.get()
            if not position_name:
                self.log("❌ 保存失败：请先选择一个点位，或添加一个新点位。", "red")
                return

            # 从输入框获取坐标值
            coords = []
            for i, entry in enumerate(self.joint_entries):
                try:
                    value = float(entry.get())
                    coords.append(value)
                except ValueError:
                    self.log(f"❌ 关节 J{i+1} 的值无效: '{entry.get()}'", "red")
                    return
            
            if len(coords) != 6:
                self.log("❌ 保存失败：必须有6个有效的关节坐标。", "red")
                return

            # 保存到坐标字典
            self.robot_positions[position_name] = coords

            self.log(f"✅ 已更新点位 '{position_name}': {coords}", "green")

        except Exception as e:
            self.log(f"❌ 保存点位失败: {e}", "red")

    def load_positions_config(self):
        """从文件加载点位配置"""
        try:
            import tkinter.filedialog as fd

            filename = fd.askopenfilename(
                title="选择点位配置文件",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                parent=self
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                # 清空现有字典
                self.robot_positions.clear()

                # 根据文件结构解析
                if isinstance(loaded_data, list): # 处理 point.json 格式
                    for point_data in loaded_data:
                        name = point_data.get("name")
                        joint_coords = point_data.get("joint")
                        if name and joint_coords and len(joint_coords) == 6:
                            self.robot_positions[name] = joint_coords
                elif isinstance(loaded_data, dict): # 处理我们自己保存的格式
                    self.robot_positions = loaded_data
                else:
                    self.log(f"❌ 文件 {filename} 格式无法识别。", "red")
                    return

                # 更新下拉框选项
                self.position_combo.configure(values=list(self.robot_positions.keys()))
                if self.robot_positions:
                    first_point = list(self.robot_positions.keys())[0]
                    self.position_var.set(first_point)
                    self.on_position_selected(first_point)
                else:
                    self.position_var.set("")
                    self.on_position_selected("")


                self.log(f"✅ 已从 {filename} 加载 {len(self.robot_positions)} 个点位", "green")

        except Exception as e:
            self.log(f"❌ 加载点位配置失败: {e}", "red")

    def save_positions_config(self):
        """保存当前所有点位配置到文件"""
        try:
            import tkinter.filedialog as fd

            filename = fd.asksaveasfilename(
                title="保存点位配置文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                parent=self
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    # 我们保存为更简洁的 {name: coords} 格式
                    json.dump(self.robot_positions, f, indent=4, ensure_ascii=False)

                self.log(f"✅ 已保存点位配置到 {filename}", "green")
                self.log(f"📍 保存了 {len(self.robot_positions)} 个点位", "cyan")

        except Exception as e:
            self.log(f"❌ 保存点位配置失败: {e}", "red")

    def _load_initial_positions(self):
        """在程序启动时自动加载 point.json 文件"""
        try:
            # 假设 point.json 和 pro.py 在同一目录下
            with open('point.json', 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 解析json数据并填充到 self.robot_positions
            # point.json 是一个字典列表，我们需要转换为 {name: joint_coords}
            for point_data in data:
                name = point_data.get("name")
                joint_coords = point_data.get("joint")
                if name and joint_coords and len(joint_coords) == 6:
                    self.robot_positions[name] = joint_coords

            self.log(f"✅ 成功从 'point.json' 加载了 {len(self.robot_positions)} 个点位。", "green")

        except FileNotFoundError:
            self.log("⚠️ 未找到 'point.json' 文件。将使用默认点位。", "orange")
            # 如果文件不存在，使用代码中原有的硬编码点位作为后备
            self.robot_positions = {
                "P7": [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0],
                "P_Detection1": [1.9212, 69.2963, 66.6202, -0.3064, 0.0, 0.0],
                "P_Detection2": [-3.0048, 23.4193, 23.358, 39.7952, 0.0, 0.0],
                "P_Safe_Height": [-2.3649, 29.5808, 31.8776, -27.4136, 0.0, 0.0],
                "P_Assembly1": [-35.0, 60.0, 45.0, 30.0, 0.0, 0.0],
            }
        except json.JSONDecodeError:
            self.log("❌ 'point.json' 文件格式错误，无法解析。", "red")
        except Exception as e:
            self.log(f"❌ 加载 'point.json' 时发生未知错误: {e}", "red")

        # 更新UI下拉框
        if self.robot_positions:
            self.position_combo.configure(values=list(self.robot_positions.keys()))
            # 默认选中第一个点位并显示其坐标
            first_point = list(self.robot_positions.keys())[0]
            self.position_var.set(first_point)
            self.on_position_selected(first_point)
        else:
            self.log("❌ 没有任何点位信息被加载。", "red")

    def add_new_position(self):
        """弹出一个对话框来添加新的点位"""
        new_name = tkinter.simpledialog.askstring("添加新点位", "请输入新点位的名称:", parent=self)
        if new_name:
            new_name = new_name.strip()
            if new_name in self.robot_positions:
                self.log(f"❌ 添加失败：点位名称 '{new_name}' 已存在。", "red")
                tkinter.messagebox.showerror("错误", f"点位名称 '{new_name}' 已存在。", parent=self)
                return
            if not new_name:
                self.log(f"❌ 添加失败：点位名称不能为空。", "red")
                tkinter.messagebox.showerror("错误", "点位名称不能为空。", parent=self)
                return

            # 使用默认坐标添加新点位
            self.robot_positions[new_name] = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
            
            # 更新UI下拉框
            current_values = list(self.position_combo.cget("values"))
            current_values.append(new_name)
            self.position_combo.configure(values=current_values)
            
            # 选中新点位
            self.position_var.set(new_name)
            self.on_position_selected(new_name)
            self.log(f"✅ 成功添加新点位 '{new_name}'。请在下方编辑其坐标或获取当前位置。", "green")

    def delete_selected_position(self):
        """删除当前在下拉框中选中的点位"""
        position_name = self.position_var.get()
        if not position_name:
            self.log("⚠️ 没有选中任何点位。", "orange")
            tkinter.messagebox.showwarning("警告", "请先在下拉框中选择一个要删除的点位。", parent=self)
            return

        # 弹出确认对话框
        confirmed = tkinter.messagebox.askyesno(
            "确认删除",
            f"您确定要删除点位 '{position_name}' 吗？\n此操作无法撤销。",
            parent=self
        )

        if confirmed:
            if position_name in self.robot_positions:
                del self.robot_positions[position_name]
                
                # 更新UI下拉框
                new_values = list(self.robot_positions.keys())
                self.position_combo.configure(values=new_values)
                
                # 选中剩余的第一个点位（如果还有的话）
                if new_values:
                    new_selection = new_values[0]
                    self.position_var.set(new_selection)
                    self.on_position_selected(new_selection)
                else:
                    # 如果都删光了，清空输入框
                    self.position_var.set("")
                    for entry in self.joint_entries:
                        entry.delete(0, "end")
                
                self.log(f"🗑️ 点位 '{position_name}' 已被删除。", "yellow")
            else:
                self.log(f"❌ 删除失败：点位 '{position_name}' 未找到。", "red")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

    def get_assembly_position(self, material_id: str) -> str:
        """根据物料ID获取对应的装配区2位置"""
        position_map = {
            "1": "P_Material1_D2",
            "2": "P_Material2_D2",
            "3": "P_Material3_D2",
            "4": "P_Material4_D2",
            "5": "P_Material5_D2",
            "6": "P_Material6_D2"
        }
        return position_map.get(material_id, "P_Assembly1")  # 默认返回废料区

    # ▼▼▼▼▼ 【增强机械臂控制功能 - V18.0 新增】 ▼▼▼▼▼
    def safe_move_j(self, point_expression: str, sync: bool = True) -> bool:
        """安全的关节运动, point_expression可以是点位名称或坐标"""
        try:
            # 统一使用 tool=0, user=0
            send_cmd(self.motion_socket, "Tool(0)", "MOT")
            send_cmd(self.motion_socket, "User(0)", "MOT")

            # 检查point_expression是名称还是坐标
            if point_expression in self.robot_positions:
                coords = self.robot_positions[point_expression]
                cmd_core = f"{{{coords[0]},{coords[1]},{coords[2]},{coords[3]},{coords[4]},{coords[5]}}}"
            else: # 假设是直接的坐标表达式
                cmd_core = point_expression
            
            if sync:
                cmd = f"MovJ({cmd_core}, \"SYNC=1\")"
            else:
                cmd = f"MovJ({cmd_core})"

            success = send_cmd(self.motion_socket, cmd, "MOT")
            if sync and success:
                send_cmd(self.dashboard_socket, "Sync()", "DASH")
                time.sleep(0.1)

            return success
        except Exception as e:
            self.log(f"❌ 安全关节运动失败: {e}", "red")
            return False

    def safe_move_l(self, point_expression: str, sync: bool = True) -> bool:
        """安全的直线运动, point_expression可以是点位名称或坐标"""
        try:
            # 检查point_expression是名称还是坐标
            if point_expression in self.robot_positions:
                coords = self.robot_positions[point_expression]
                cmd_core = f"{{{coords[0]},{coords[1]},{coords[2]},{coords[3]},{coords[4]},{coords[5]}}}"
            else: # 假设是直接的坐标表达式
                cmd_core = point_expression

            if sync:
                cmd = f"MovL({cmd_core}, \"SYNC=1\")"
            else:
                cmd = f"MovL({cmd_core})"

            success = send_cmd(self.motion_socket, cmd, "MOT")
            if sync and success:
                send_cmd(self.dashboard_socket, "Sync()", "DASH")
                time.sleep(0.1)

            return success
        except Exception as e:
            self.log(f"❌ 安全直线运动失败: {e}", "red")
            return False

    def safe_move_to_coordinates(self, x: float, y: float, z: float, r: float, sync: bool = True) -> bool:
        """安全移动到指定笛卡尔坐标"""
        try:
            cmd_core = f"{{{x},{y},{z},{r}}}"
            if sync:
                cmd = f"MovL({cmd_core}, \"SYNC=1\")"
            else:
                cmd = f"MovL({cmd_core})"

            success = send_cmd(self.motion_socket, cmd, "MOT")
            if sync and success:
                send_cmd(self.dashboard_socket, "Sync()", "DASH")
                time.sleep(0.1)

            return success
        except Exception as e:
            self.log(f"❌ 移动到坐标失败: {e}", "red")
            return False

    def control_suction(self, state: bool) -> bool:
        """控制吸盘状态"""
        try:
            if not self.is_robot_connected:
                self.log("❌ 吸盘控制失败: 机器人未连接", "red")
                return False

            cmd = f"DO(1, {1 if state else 0})"
            action = "开启" if state else "关闭"
            self.log(f"🔌 正在{action}吸盘: {cmd}", "yellow")

            success = send_cmd(self.motion_socket, cmd, "MOT")
            if success:
                self.log(f"✅ 吸盘{action}成功", "green")
                time.sleep(0.2)
            else:
                self.log(f"❌ 吸盘{action}失败", "red")
            return success
        except Exception as e:
            self.log(f"❌ 控制吸盘异常: {e}", "red")
            return False

    def control_air_blow(self, state: bool) -> bool:
        """控制喷气状态"""
        try:
            cmd = f"DO(2, {1 if state else 0})"
            success = send_cmd(self.motion_socket, cmd, "MOT")
            action = "开启" if state else "关闭"
            if success:
                self.log(f"✅ 喷气{action}成功", "green")
            else:
                self.log(f"❌ 喷气{action}失败", "red")
            return success
        except Exception as e:
            self.log(f"❌ 控制喷气失败: {e}", "red")
            return False

    def enhanced_pick_material(self, x: float, y: float, z: float, r: float) -> bool:
        """增强的物料抓取功能（基于lua代码）"""
        try:
            self.log("🤏 开始增强抓取物料...", "cyan")

            # 统一配置工具 (tool=0, user=0)
            send_cmd(self.motion_socket, "Tool(0)", "MOT")
            send_cmd(self.motion_socket, "User(0)", "MOT")

            pick_pos_safe_z = z + 50
            pick_pos_z = -184.72

            if not self.control_suction(True): return False
            if not self.safe_move_to_coordinates(x, y, pick_pos_safe_z, r): return False
            time.sleep(0.2)
            if not self.safe_move_to_coordinates(x, y, pick_pos_z, r): return False
            time.sleep(0.5)
            if not self.safe_move_to_coordinates(x, y, pick_pos_safe_z, r): return False
            time.sleep(0.2)

            self.log("✅ 增强物料抓取完成", "green")
            return True

        except Exception as e:
            self.log(f"❌ 增强抓取操作失败: {e}", "red")
            return False

    def enhanced_place_material(self, target_point: str, r: float) -> bool:
        """增强的物料放置功能（基于lua代码）"""
        try:
            self.log(f"📍 开始增强放置物料到 {target_point}...", "cyan")
            if target_point not in self.robot_positions:
                self.log(f"❌ 放置失败: 点位 '{target_point}' 未定义。", "red")
                return False

            send_cmd(self.motion_socket, "Tool(0)", "MOT")
            send_cmd(self.motion_socket, "User(0)", "MOT")

            # 获取目标点位的关节坐标，并计算上方点
            target_coords = self.robot_positions[target_point]
            # 这里简化处理，直接使用RP指令，它依赖于机器人控制器中定义的点位
            # 为了独立于机器人控制器，我们需要手动计算上方点坐标，这很复杂。
            # 这里我们假设一个简化的逻辑：先移动到目标点，然后处理IO
            # 注意：这不如lua中的RP指令安全
            
            # 移动到安全高度
            if not self.safe_move_j("P_Safe_Height"): return False
            
            # 移动到目标位置
            if not self.safe_move_j(target_point): return False
            time.sleep(0.2)
            
            if not self.control_suction(False): return False
            time.sleep(0.3)
            if not self.control_air_blow(True): return False
            time.sleep(0.5)
            if not self.control_air_blow(False): return False
            
            # 提升到安全高度
            if not self.safe_move_j("P_Safe_Height"): return False
            time.sleep(0.2)
            
            self.log("✅ 增强物料放置完成", "green")
            return True

        except Exception as e:
            self.log(f"❌ 增强放置操作失败: {e}", "red")
            return False

    def pick_and_place_with_lua_logic(self, material_id: str, x: float, y: float, z: float, r: float) -> bool:
        """基于lua代码逻辑的完整抓取和放置流程"""
        try:
            self.log(f"🔄 开始lua逻辑分拣流程 - 物料ID: {material_id}", "cyan")

            if not self.enhanced_pick_material(x, y, z, r): return False
            
            target_point = "P_Assembly1" # 默认为废料区
            
            try:
                material_id_num = int(material_id)
                if 1 <= material_id_num <= 6:
                    self.log(f"✅ 检测到物料芯片ID: {material_id_num}，进入两阶段检测", "green")
                    
                    self.log("🚚 移动到检测区2进行背面检测...", "cyan")
                    if self.slide_controller.is_connected:
                        success = self.slide_controller.move_to_absolute_position(self.detection2_position)
                    else:
                        distance_to_move = self.detection2_position - self.current_conveyor_position
                        cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                        success = send_cmd(self.motion_socket, cmd, "MOT")

                    if success:
                        self.current_conveyor_position = self.detection2_position
                        self.update_conveyor_display()

                        if not self.safe_move_j("P_Detection2"): return False
                        time.sleep(0.5)

                        detection_result = self.trigger_vision_detection_zone2()

                        if detection_result == "OK":
                            target_point = self.get_assembly_position(material_id)
                            self.log(f"✅ 背面检测OK，放置到装配区2位置: {target_point}", "green")
                        else:
                            self.log("⚠️ 背面检测NG或失败，将放置到废料区", "orange")
                            target_point = "P_Assembly1"
                    else:
                        self.log("❌ 传送带移动到检测区2失败, 将放置于废料区", "red")
                        target_point = "P_Assembly1"
                else:
                    self.log(f"⚠️ 无效物料ID: {material_id}，放置到废料区", "orange")
            except (ValueError, TypeError):
                self.log(f"⚠️ 未识别或非数字物料ID: '{material_id}'，放置到废料区", "orange")

            # 执行放置
            self.log(f"🚚 移动回初始检测区准备放置...", "cyan")
            if self.slide_controller.is_connected:
                success = self.slide_controller.move_to_absolute_position(self.detection1_position)
            else:
                distance_to_move = self.detection1_position - self.current_conveyor_position
                cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                success = send_cmd(self.motion_socket, cmd, "MOT")

            if success:
                self.current_conveyor_position = self.detection1_position
                self.update_conveyor_display()
                return self.enhanced_place_material(target_point, r)
            else:
                self.log("❌ 传送带移动回初始区失败", "red")
                # 尝试在当前位置进行放置（作为后备方案）
                return self.enhanced_place_material(target_point, r)

        except Exception as e:
            self.log(f"❌ lua逻辑分拣流程失败: {e}", "red")
            return False
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

    # ▼▼▼▼▼ 【TCP通信功能 - V18.0 重新设计】 ▼▼▼▼▼
    def connect_to_vision_servers(self) -> bool:
        """连接到视觉软件的服务端（Python作为客户端）"""
        self.log("🔗 正在连接到视觉软件服务端...", "cyan")

        success1 = self.connect_detection_zone(1)
        success2 = self.connect_detection_zone(2)

        if success1 and success2:
            self.log("✅ 所有视觉服务端连接建立完成", "green")
            return True
        else:
            self.log("⚠️ 部分视觉服务端连接失败", "orange")
            return False

    def connect_detection_zone(self, zone_number: int) -> bool:
        """连接指定检测区的视觉服务端"""
        try:
            if zone_number == 1:
                port = self.detection1_port
                socket_attr = 'detection1_socket'
                zone_name = "检测区1"
            elif zone_number == 2:
                port = self.detection2_port
                socket_attr = 'detection2_socket'
                zone_name = "检测区2"
            else:
                self.log(f"❌ 无效的检测区编号: {zone_number}", "red")
                return False

            # 关闭现有连接
            if hasattr(self, socket_attr) and getattr(self, socket_attr):
                try:
                    getattr(self, socket_attr).close()
                except:
                    pass

            # 创建新连接到视觉软件服务端
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    sock = socket.create_connection((self.vision_ip, port), timeout=5)
                    setattr(self, socket_attr, sock)
                    self.log(f"✅ {zone_name} 连接到视觉服务端成功 (IP:{self.vision_ip}, 端口:{port})", "green")
                    self.update_tcp_status(zone_number, "已连接", "lightgreen")
                    return True
                except Exception as e:
                    self.log(f"⚠️ {zone_name} 连接尝试 {attempt + 1}/{max_retries} 失败: {e}", "orange")
                    if attempt < max_retries - 1:
                        time.sleep(1)

            self.log(f"❌ {zone_name} 连接到视觉服务端失败", "red")
            self.update_tcp_status(zone_number, "失败", "red")
            return False

        except Exception as e:
            self.log(f"❌ 连接{zone_name}时发生错误: {e}", "red")
            self.update_tcp_status(zone_number, "错误", "red")
            return False

    def send_command_to_detection_zone(self, zone_number: int, command: str) -> bool:
        """发送指令到指定检测区"""
        try:
            if zone_number == 1:
                sock = self.detection1_socket
                zone_name = "检测区1"
            elif zone_number == 2:
                sock = self.detection2_socket
                zone_name = "检测区2"
            else:
                return False

            if not sock:
                self.log(f"❌ {zone_name}未连接，尝试重连...", "orange")
                if not self.connect_detection_zone(zone_number):
                    return False
                sock = getattr(self, f'detection{zone_number}_socket')

            # 发送指令
            message = (command + "\n").encode('utf-8')
            sock.sendall(message)
            self.log(f"📤 已发送指令到{zone_name}: {command}", "cyan")
            return True

        except Exception as e:
            self.log(f"❌ 发送指令到{zone_name}失败: {e}。将关闭连接。", "red")
            try:
                sock.close()
            except: pass
            setattr(self, f'detection{zone_number}_socket', None)
            self.update_tcp_status(zone_number, "断开", "red")
            return False

    def read_response_from_detection_zone(self, zone_number: int, timeout: float = 5.0) -> str:
        """从指定检测区读取响应"""
        try:
            if zone_number == 1:
                sock = self.detection1_socket
                zone_name = "检测区1"
            elif zone_number == 2:
                sock = self.detection2_socket
                zone_name = "检测区2"
            else:
                return None

            if not sock:
                self.log(f"❌ {zone_name}未连接", "red")
                return None

            sock.settimeout(timeout)
            
            with sock.makefile('r') as f:
                response = f.readline().strip()

            self.log(f"📥 接收到{zone_name}数据: {response}", "cyan")
            return response

        except socket.timeout:
            self.log(f"⏰ {zone_name}响应超时", "orange")
            return None
        except Exception as e:
            self.log(f"❌ 从{zone_name}读取响应失败: {e}。将关闭连接。", "red")
            try:
                sock.close()
            except: pass
            setattr(self, f'detection{zone_number}_socket', None)
            self.update_tcp_status(zone_number, "断开", "red")
            return None

    def trigger_vision_detection_zone1(self) -> tuple:
        """触发检测区1视觉检测，发送"ok"指令并获取坐标信息"""
        try:
            if not self.send_command_to_detection_zone(1, "ok"):
                return None, None, None, None

            response = self.read_response_from_detection_zone(1, timeout=10.0)
            if response:
                return self.parse_detection1_response(response)
            
            self.log("❌ 检测区1未收到有效响应", "red")
            return None, None, None, None

        except Exception as e:
            self.log(f"❌ 检测区1视觉检测失败: {e}", "red")
            return None, None, None, None

    def trigger_vision_detection_zone2(self) -> str:
        """触发检测区2视觉检测，发送"start"指令并获取OK/NG结果"""
        try:
            if not self.send_command_to_detection_zone(2, "start"):
                return None
            
            response = self.read_response_from_detection_zone(2, timeout=10.0)
            if response:
                return self.parse_detection2_response(response)
            
            self.log("❌ 检测区2未收到有效响应", "red")
            return None

        except Exception as e:
            self.log(f"❌ 检测区2视觉检测失败: {e}", "red")
            return None

    def parse_detection1_response(self, response: str) -> tuple:
        """解析检测区1的响应数据"""
        try:
            delimiters = [';', ':', ',']
            parts = []
            for d in delimiters:
                if d in response:
                    parts = response.split(d)
                    break
            else:
                parts = response.split()

            if len(parts) >= 4:
                material_id = parts[0].strip()
                x = float(parts[1])
                y = float(parts[2])
                r = float(parts[3])
                self.log(f"✅ 解析成功 - ID:{material_id}, X:{x:.2f}, Y:{y:.2f}, R:{r:.2f}", "green")
                return material_id, x, y, r
            
            self.log(f"⚠️ 检测区1数据格式无法解析 (需要至少4个部分): {response}", "orange")
            return None, None, None, None

        except (ValueError, IndexError) as e:
            self.log(f"❌ 检测区1数据解析错误: {e}, 响应: '{response}'", "red")
            return None, None, None, None

    def parse_detection2_response(self, response: str) -> str:
        """解析检测区2的响应数据"""
        try:
            result = response.upper().strip()
            if "OK" in result:
                return "OK"
            elif "NG" in result:
                return "NG"
            else:
                self.log(f"⚠️ 检测区2结果格式错误: {response}", "orange")
                return None
        except Exception as e:
            self.log(f"❌ 检测区2数据解析错误: {e}", "red")
            return None

    def close_tcp_connections(self):
        """关闭所有TCP连接"""
        try:
            if hasattr(self, 'detection1_socket') and self.detection1_socket:
                self.detection1_socket.close()
                self.detection1_socket = None
                self.update_tcp_status(1, "未连接", "gray")
                self.log("🔌 检测区1 TCP连接已关闭", "gray")

            if hasattr(self, 'detection2_socket') and self.detection2_socket:
                self.detection2_socket.close()
                self.detection2_socket = None
                self.update_tcp_status(2, "未连接", "gray")
                self.log("🔌 检测区2 TCP连接已关闭", "gray")

        except Exception as e:
            self.log(f"❌ 关闭TCP连接时发生错误: {e}", "red")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

    # (其他函数无变化...)
    def log(self, message, color="white"):
        self.log_textbox.configure(state="normal"); self.log_textbox.tag_config(f"tag_{color}", foreground=color); self.log_textbox.insert("end", f"{time.strftime('%H:%M:%S')} - {message}\n", f"tag_{color}"); self.log_textbox.configure(state="disabled"); self.log_textbox.see("end")
    def handle_connect_button_click(self):
        if not self.is_robot_connected:
            try:
                self.log(f"正在连接机器人 at {ROBOT_IP}...", "cyan"); self.dashboard_socket = socket.create_connection((ROBOT_IP, DASHBOARD_PORT), timeout=5); self.motion_socket = socket.create_connection((ROBOT_IP, MOTION_PORT), timeout=5)
                self.log("正在尝试使能机器人...", "yellow")
                if not send_cmd(self.dashboard_socket, "EnableRobot()", "DASH"): raise ConnectionError("机器人使能失败")
                self.log("机器人已使能，等待伺服系统稳定...", "yellow"); time.sleep(1)
                self.is_robot_connected = True; self.log("✅ 机器人连接并使能成功!", "green"); self.connect_label.configure(text="机器人已连接", text_color="green"); self.btn_connect.configure(text="断开连接")
            except Exception as e:
                self.log(f"❌ 连接失败: {e}", "red")
                if self.dashboard_socket: self.dashboard_socket.close()
                if self.motion_socket: self.motion_socket.close()
        else:
            send_cmd(self.dashboard_socket, "DisableRobot()", "DASH");
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close();
            self.is_robot_connected = False; self.log("🔌 机器人已断开。", "orange"); self.connect_label.configure(text="机器人未连接", text_color="orange"); self.btn_connect.configure(text="连接机器人")
    def trigger_vision_capture(self):
        self.log("📸 发送拍照触发指令...", "yellow")
        try:
            with socket.create_connection((PYTHON_PC_IP, VISION_TRIGGER_PORT), timeout=3) as s:
                cmd_to_send = VISION_TRIGGER_CMD + "\n"; s.sendall(cmd_to_send.encode('utf-8')); self.log("✅ 触发指令已发送成功。", "green")
        except socket.timeout: self.log(f"❌ 触发失败: 连接视觉软件({PYTHON_PC_IP}:{VISION_TRIGGER_PORT})超时。", "red")
        except Exception as e: self.log(f"❌ 触发失败: {e}", "red")
    def start_jog(self, axis_id):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log(f"🤖 开始点动: {axis_id}", "cyan"); send_cmd(self.motion_socket, f"MoveJog({axis_id})", "MOT")
    def stop_jog(self, event=None):
        if not self.is_robot_connected: return
        self.log("🤖 停止点动", "cyan"); send_cmd(self.motion_socket, "MoveJog()", "MOT"); time.sleep(0.2)
    def go_home(self):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return False
        self.log("🤖 正在移动到安全原点...");
        # 使用一个已知的安全点位，或者一个绝对安全的坐标
        home_point = "P_Safe_Height"
        if home_point in self.robot_positions:
            if self.move_to_position(home_point):
                 self.log("✅ 已到达原点。")
                 return True
            else:
                 self.log("❌ 回原点失败。", "red")
                 return False
        else:
            self.log("⚠️ 未定义原点'P_Safe_Height'，使用绝对坐标回原点", "orange")
            if send_cmd(self.motion_socket, "MovJ({0,30,60,0,0,0})", "MOT"):
                send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("✅ 已到达原点。")
                return True
            else: self.log("❌ 回原点失败。", "red"); return False
            
    def execute_pick_and_place(self, target_x, target_y, target_r):
        if not self.is_robot_connected: self.log("⚠️ 自动抓取失败：机器人未连接", "orange"); return
        self.log(f"🤖 开始执行抓取任务...");
        self.pick_and_place_with_lua_logic("?", target_x, target_y, 0, target_r)

    def process_vision_queue(self):
        """处理视觉数据队列"""
        try:
            # 处理原有队列（保持兼容性）
            while not self.vision_queue.empty():
                message = self.vision_queue.get_nowait()
                self.log(f"📩 收到视觉数据包(旧): {message}", "cyan")
                parts = message.split(';')
                if len(parts) > 1:
                    image_path = parts[-1].strip()
                    self.show_image_from_path(image_path)
                if len(parts) >= 2 and self.auto_run_switch.get() and self.is_robot_connected:
                    coord_data = parts[0]
                    try:
                        coord_parts = coord_data.split(',');
                        if len(coord_parts) >= 2:
                            robot_x, robot_y = float(coord_parts[0]), float(coord_parts[1]); robot_r = float(coord_parts[2]) if len(coord_parts) > 2 else 0.0
                            self.execute_pick_and_place(robot_x, robot_y, robot_r)
                        else: self.log(f"⚠️ 坐标部分格式无法解析: {coord_data}", "orange")
                    except (ValueError, IndexError) as e: self.log(f"❌ 解析坐标数据失败: {e}", "red")

        except queue.Empty:
            pass
        except Exception as e:
            self.log(f"❌ 处理视觉队列时出错: {e}", "red")

        self.after(100, self.process_vision_queue)

    def update_tcp_status(self, zone: int, status: str, color: str = "white"):
        """更新TCP连接状态显示"""
        try:
            if zone == 1:
                self.label_tcp6005_status.configure(text=status, text_color=color)
            elif zone == 2:
                self.label_tcp6006_status.configure(text=status, text_color=color)
        except Exception as e:
            self.log(f"❌ 更新TCP状态失败: {e}", "red")
            
    def show_image_from_path(self, image_path):
        max_retries = 5; retry_delay = 0.2
        for attempt in range(max_retries):
            if os.path.exists(image_path):
                try:
                    with Image.open(image_path) as image: image.verify()
                    with Image.open(image_path) as image:
                        image.thumbnail((self.image_display_label.winfo_width(), self.image_display_label.winfo_height()), Image.Resampling.LANCZOS)
                        ctk_image = ImageTk.PhotoImage(image)
                        self.image_display_label.configure(image=ctk_image, text=""); self.image_display_label.image = ctk_image
                        self.log(f"✅ 图像显示成功。(尝试第 {attempt + 1} 次)", "green"); return
                except (IOError, SyntaxError) as e: self.log(f"   - 第 {attempt + 1} 次尝试：文件不完整 ({e})，稍后重试...", "yellow"); time.sleep(retry_delay)
                except PermissionError: self.log(f"   - 第 {attempt + 1} 次尝试：文件被占用，稍后重试...", "yellow"); time.sleep(retry_delay)
                except Exception as e: self.log(f"❌ 显示图像时发生未知错误: {e}", "red"); return
            else: self.log(f"❌ 找不到图像文件: {image_path}", "red"); return
        self.log(f"❌ 图像加载失败：在 {max_retries} 次尝试后依然无法读取文件。", "red")

    def vision_listener_thread(self):
        """保持兼容性的原始监听方法"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT)); s.listen()
                self.log(f"👂 视觉服务器(旧)已启动，正在监听端口 {VISION_SERVER_PORT}...", "gray")
                while True:
                    conn, addr = s.accept()
                    with conn:
                        self.log(f"🤝 视觉软件(旧)已连接: {addr}", "gray")
                        while True:
                            data = conn.recv(1024)
                            if not data:
                                self.log("🔌 视觉软件(旧)已断开。", "gray")
                                break
                            self.vision_queue.put(data.decode('utf-8').strip())
            except OSError as e:
                self.log(f"❌ 端口 {VISION_SERVER_PORT} 绑定失败: {e}。旧版视觉监听可能无法工作。", "red")

    def on_closing(self):
        self.log("🛑 程序正在关闭...", "yellow")
        # 停止自动化流程
        if self.automation_running:
            self.log("   正在停止自动化流程...")
            self.automation_running = False
            if self.automation_thread and self.automation_thread.is_alive():
                self.automation_thread.join(timeout=2)

        # 关闭TCP连接
        self.close_tcp_connections()

        # 断开机器人连接
        if self.is_robot_connected:
            self.log("   正在断开机器人连接...");
            send_cmd(self.dashboard_socket, "DisableRobot()", "DASH")
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close()
        
        self.destroy()

if __name__ == "__main__":
    # position_manager 模块是用于位置记录和验证的，如果不需要可以注释掉
    # from position_manager import position_manager
    
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = RobotControlApp()
    app.mainloop()
