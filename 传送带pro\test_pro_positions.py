#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pro.py中的点位定义
验证所有点位坐标是否正确定义
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pro_positions():
    """测试pro.py中的点位定义"""
    print("🧪 测试pro.py中的点位定义...")
    
    try:
        # 导入pro.py中的RobotControlApp类
        from pro import RobotControlApp
        
        # 创建应用实例（不启动GUI）
        print("📦 创建RobotControlApp实例...")
        app = RobotControlApp()
        
        # 检查robot_positions字典
        print(f"\n📋 检查robot_positions字典...")
        print(f"   字典类型: {type(app.robot_positions)}")
        print(f"   点位数量: {len(app.robot_positions)}")
        
        if not app.robot_positions:
            print("❌ robot_positions字典为空！")
            return False
        
        print(f"\n📍 所有定义的点位:")
        for i, (point_name, coords) in enumerate(app.robot_positions.items(), 1):
            print(f"   {i:2d}. {point_name}: {coords}")
            
            # 验证坐标格式
            if not isinstance(coords, (list, tuple)):
                print(f"      ❌ 坐标格式错误: {type(coords)}")
                continue
                
            if len(coords) != 6:
                print(f"      ❌ 坐标长度错误: {len(coords)} (应为6)")
                continue
                
            # 检查坐标值是否为数字
            try:
                for j, coord in enumerate(coords):
                    float(coord)  # 尝试转换为浮点数
                print(f"      ✅ 坐标格式正确")
            except (ValueError, TypeError) as e:
                print(f"      ❌ 坐标值错误: {e}")
        
        # 检查关键点位
        print(f"\n🔍 检查关键点位:")
        key_positions = [
            "P7", "P_Camera_Pos", 
            "P_Detection1", "P_Detection2",
            "P_Safe_Height", "P_Assembly1",
            "InitialPose"
        ]
        
        missing_positions = []
        for pos in key_positions:
            if pos in app.robot_positions:
                coords = app.robot_positions[pos]
                coord_type = "笛卡尔坐标" if pos in ["P7", "P_Camera_Pos"] else "关节角度"
                print(f"   ✅ {pos}: {coords} ({coord_type})")
            else:
                missing_positions.append(pos)
                print(f"   ❌ {pos}: 未找到")
        
        if missing_positions:
            print(f"\n⚠️ 缺少关键点位: {missing_positions}")
        else:
            print(f"\n✅ 所有关键点位都已定义")
        
        # 检查P7坐标是否为指定值
        print(f"\n🎯 验证P7坐标:")
        expected_p7 = [247.55, 239.02, -83.49, 0.5334, 0.0, 0.0]
        if "P7" in app.robot_positions:
            actual_p7 = app.robot_positions["P7"]
            print(f"   期望坐标: {expected_p7}")
            print(f"   实际坐标: {actual_p7}")
            
            if actual_p7 == expected_p7:
                print(f"   ✅ P7坐标正确")
            else:
                print(f"   ❌ P7坐标不匹配")
                # 检查每个值
                for i, (exp, act) in enumerate(zip(expected_p7, actual_p7)):
                    if abs(exp - act) > 0.001:  # 允许小的浮点误差
                        print(f"      位置{i}: 期望{exp}, 实际{act}")
        else:
            print(f"   ❌ P7未定义")
        
        print(f"\n📊 总结:")
        print(f"   总点位数: {len(app.robot_positions)}")
        print(f"   关键点位: {len(key_positions) - len(missing_positions)}/{len(key_positions)}")
        
        if len(app.robot_positions) > 0 and len(missing_positions) == 0:
            print(f"   ✅ 点位定义完整")
            return True
        else:
            print(f"   ❌ 点位定义不完整")
            return False
            
    except ImportError as e:
        print(f"❌ 导入pro.py失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_position_access():
    """测试点位访问方法"""
    print(f"\n🔧 测试点位访问方法...")
    
    try:
        from pro import RobotControlApp
        app = RobotControlApp()
        
        # 测试点位访问
        test_points = ["P7", "P_Detection1", "InitialPose"]
        
        for point in test_points:
            if point in app.robot_positions:
                coords = app.robot_positions[point]
                print(f"   ✅ {point}: 可访问 -> {coords}")
                
                # 测试坐标格式化
                if point in ["P7", "P_Camera_Pos"]:
                    cmd = f"MovL({coords[0]}, {coords[1]}, {coords[2]}, {coords[3]})"
                else:
                    cmd = f"MovJ({{{coords[0]}, {coords[1]}, {coords[2]}, {coords[3]}, {coords[4]}, {coords[5]}}})"
                print(f"      指令: {cmd}")
            else:
                print(f"   ❌ {point}: 不可访问")
        
        return True
        
    except Exception as e:
        print(f"❌ 点位访问测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 pro.py点位定义测试")
    print("=" * 50)
    
    # 测试点位定义
    positions_ok = test_pro_positions()
    
    # 测试点位访问
    access_ok = test_position_access()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   点位定义: {'✅ 通过' if positions_ok else '❌ 失败'}")
    print(f"   点位访问: {'✅ 通过' if access_ok else '❌ 失败'}")
    
    if positions_ok and access_ok:
        print("\n🎉 所有测试通过！pro.py中的点位定义正常。")
        print("\n💡 如果pro.py中仍显示点位不存在，可能的原因:")
        print("   1. 机器人未连接")
        print("   2. 点位检查函数逻辑问题")
        print("   3. GUI更新问题")
        print("\n🔧 建议:")
        print("   1. 确保机器人已连接")
        print("   2. 重新启动pro.py")
        print("   3. 点击'检查点位'按钮")
    else:
        print("\n❌ 测试失败，需要检查pro.py中的点位定义。")

if __name__ == "__main__":
    main()
