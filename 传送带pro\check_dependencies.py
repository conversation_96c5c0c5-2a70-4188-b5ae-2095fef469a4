#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查vision_demo.py所需的依赖
"""

print("🔍 检查Python依赖...")

# 检查基础模块
modules_to_check = [
    'sys', 'os', 'socket', 'threading', 'time', 'datetime',
    'tkinter', 'numpy', 'cv2', 'PIL'
]

missing_modules = []
available_modules = []

for module in modules_to_check:
    try:
        if module == 'tkinter':
            import tkinter
            available_modules.append(f"✅ {module}")
        elif module == 'numpy':
            import numpy as np
            available_modules.append(f"✅ {module} (版本: {np.__version__})")
        elif module == 'cv2':
            import cv2
            available_modules.append(f"✅ {module} (版本: {cv2.__version__})")
        elif module == 'PIL':
            from PIL import Image
            available_modules.append(f"✅ {module}")
        else:
            __import__(module)
            available_modules.append(f"✅ {module}")
    except ImportError:
        missing_modules.append(f"❌ {module}")

print("\n📋 依赖检查结果:")
for module in available_modules:
    print(module)

if missing_modules:
    print("\n⚠️ 缺失的模块:")
    for module in missing_modules:
        print(module)
    
    print("\n💡 安装建议:")
    if "❌ cv2" in missing_modules:
        print("python -m pip install opencv-python")
    if "❌ PIL" in missing_modules:
        print("python -m pip install pillow")
    if "❌ numpy" in missing_modules:
        print("python -m pip install numpy")
else:
    print("\n✅ 所有基础依赖都已安装！")

# 检查vision_workpiece_detector
print("\n🔍 检查vision_workpiece_detector模块...")
try:
    from vision_workpiece_detector import VisionWorkpieceDetector
    print("✅ vision_workpiece_detector 导入成功")
except ImportError as e:
    print(f"❌ vision_workpiece_detector 导入失败: {e}")
except Exception as e:
    print(f"⚠️ vision_workpiece_detector 导入时出现其他错误: {e}")

print("\n🔍 检查vision_demo模块...")
try:
    import vision_demo
    print("✅ vision_demo 导入成功")
except ImportError as e:
    print(f"❌ vision_demo 导入失败: {e}")
except Exception as e:
    print(f"⚠️ vision_demo 导入时出现其他错误: {e}")

print("\n✅ 依赖检查完成！")
