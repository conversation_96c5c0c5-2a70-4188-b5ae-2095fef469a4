#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试P7坐标更新后的机械臂功能
验证新的P7坐标: x=247.55, y=239.02, z=-83.49, r=0.5334, 传送带坐标=257.46
"""

import socket
import time
import json

class P7UpdateTester:
    """P7坐标更新测试器"""
    
    def __init__(self):
        # 机器人连接配置
        self.robot_ip = "***********"
        self.dashboard_port = 29999
        self.motion_port = 30003
        
        # 连接状态
        self.dashboard_socket = None
        self.motion_socket = None
        self.is_connected = False
        
        # P7坐标（来自point.json关节角度）
        self.new_p7_coords = [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0]
        self.conveyor_position = 257.46

        # 更新后的关键点位
        self.robot_positions = {
            "P7": [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0],  # point.json关节角度
            "P_Camera_Pos": [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0],  # 与P7相同
            "P_Detection1": [1.9212, 69.2963, 66.6202, -0.3064, 0.0, 0.0],
            "P_Detection2": [-3.0048, 23.4193, 23.358, 39.7952, 0.0, 0.0],
            "P_Safe_Height": [-2.3649, 29.5808, 31.8776, -27.4136, 0.0, 0.0],
            "P_Waste_Detection1": [24.9675, 70.9075, 66.9768, 39.7678, 0.0, 0.0],
            "P_Assembly1": [-35.0, 60.0, 45.0, 30.0, 0.0, 0.0],
            "P_Material1_D2": [-22.3874, 75.56, 76.7475, 74.3033, 0.0, 0.0]
        }
        
        print("🔄 P7坐标更新测试器初始化完成")
        print(f"📍 新P7坐标: X={self.new_p7_coords[0]}, Y={self.new_p7_coords[1]}, Z={self.new_p7_coords[2]}, R={self.new_p7_coords[3]}")
        print(f"🚚 传送带坐标: {self.conveyor_position} mm")
    
    def send_cmd(self, sock, cmd) -> bool:
        """发送指令到机器人"""
        try:
            if sock is None:
                print(f"❌ Socket为空")
                return False
            
            full_cmd = cmd + "\n"
            print(f"📤 发送: {full_cmd.strip()}")
            
            sock.settimeout(5.0)
            sock.sendall(full_cmd.encode('utf-8'))
            response = sock.recv(1024).decode('utf-8').strip()
            print(f"📥 响应: {response}")
            
            if response and response.split(',')[0] == '0':
                return True
            else:
                print(f"❌ 指令失败: {response}")
                return False
                
        except Exception as e:
            print(f"❌ 发送指令出错: {e}")
            return False
    
    def connect_robot(self) -> bool:
        """连接机器人"""
        try:
            print(f"🔗 连接机器人 {self.robot_ip}...")
            
            self.dashboard_socket = socket.create_connection((self.robot_ip, self.dashboard_port), timeout=5)
            self.motion_socket = socket.create_connection((self.robot_ip, self.motion_port), timeout=5)
            
            print("🔧 使能机器人...")
            if not self.send_cmd(self.dashboard_socket, "EnableRobot()"):
                raise ConnectionError("机器人使能失败")
            
            time.sleep(1)
            self.is_connected = True
            print("✅ 机器人连接成功!")
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect_robot(self):
        """断开机器人连接"""
        try:
            if self.is_connected:
                print("🔌 断开机器人连接...")
                self.send_cmd(self.dashboard_socket, "DisableRobot()")
                
            if self.dashboard_socket:
                self.dashboard_socket.close()
            if self.motion_socket:
                self.motion_socket.close()
                
            self.is_connected = False
            print("✅ 机器人已断开")
            
        except Exception as e:
            print(f"❌ 断开连接出错: {e}")
    
    def move_to_position(self, position_name: str) -> bool:
        """移动到指定点位"""
        try:
            if not self.is_connected:
                print("❌ 机器人未连接")
                return False
                
            if position_name not in self.robot_positions:
                print(f"❌ 点位 {position_name} 不存在")
                return False
            
            coords = self.robot_positions[position_name]
            
            # 注意：这里使用的是坐标而不是关节角度
            # 根据您提供的数据，这些应该是笛卡尔坐标
            coord_cmd = f"MovL({coords[0]}, {coords[1]}, {coords[2]}, {coords[3]})"
            print(f"🤖 移动到 {position_name} (坐标模式)...")
            print(f"   坐标: X={coords[0]}, Y={coords[1]}, Z={coords[2]}, R={coords[3]}")
            
            success = self.send_cmd(self.motion_socket, coord_cmd)
            if success:
                self.send_cmd(self.dashboard_socket, "Sync()")
                print(f"✅ 成功移动到 {position_name}")
                time.sleep(1)  # 等待运动完成
            else:
                print(f"❌ 移动到 {position_name} 失败")
            
            return success
            
        except Exception as e:
            print(f"❌ 移动出错: {e}")
            return False
    
    def test_p7_position(self) -> bool:
        """专门测试新的P7位置"""
        print("\n🧪 测试新的P7位置...")
        
        try:
            # 测试移动到P7
            print("📍 测试移动到P7...")
            if not self.move_to_position("P7"):
                return False
            
            # 测试移动到P_Camera_Pos（应该与P7相同）
            print("📍 测试移动到P_Camera_Pos...")
            if not self.move_to_position("P_Camera_Pos"):
                return False
            
            print("✅ P7位置测试完成")
            return True
            
        except Exception as e:
            print(f"❌ P7位置测试失败: {e}")
            return False
    
    def test_coordinate_movement(self) -> bool:
        """测试坐标移动功能"""
        print("\n🧪 测试坐标移动功能...")
        
        try:
            # 直接使用坐标移动到P7位置
            x, y, z, r = self.new_p7_coords[:4]
            coord_cmd = f"MovL({x}, {y}, {z}, {r})"
            
            print(f"🎯 直接移动到P7坐标: X={x}, Y={y}, Z={z}, R={r}")
            success = self.send_cmd(self.motion_socket, coord_cmd)
            
            if success:
                self.send_cmd(self.dashboard_socket, "Sync()")
                print("✅ 坐标移动测试成功")
                time.sleep(1)
                return True
            else:
                print("❌ 坐标移动测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 坐标移动测试出错: {e}")
            return False
    
    def test_picking_sequence_with_new_p7(self) -> bool:
        """使用新P7坐标测试抓取序列"""
        print("\n🧪 使用新P7坐标测试抓取序列...")
        
        try:
            # 1. 移动到新的P7拍照位置
            print("1️⃣ 移动到新P7拍照位置...")
            if not self.move_to_position("P7"):
                return False
            
            # 2. 模拟拍照延时
            print("2️⃣ 模拟拍照过程...")
            time.sleep(1)
            
            # 3. 移动到检测区1
            print("3️⃣ 移动到检测区1...")
            if not self.move_to_position("P_Detection1"):
                return False
            
            # 4. 移动到安全高度
            print("4️⃣ 移动到安全高度...")
            if not self.move_to_position("P_Safe_Height"):
                return False
            
            # 5. 返回P7位置
            print("5️⃣ 返回P7位置...")
            if not self.move_to_position("P7"):
                return False
            
            print("✅ 新P7坐标抓取序列测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 抓取序列测试失败: {e}")
            return False
    
    def verify_config_files(self) -> bool:
        """验证配置文件是否已更新"""
        print("\n🔍 验证配置文件更新...")
        
        try:
            # 检查robot_positions_config.json
            with open('robot_positions_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            p7_config = config['positions']['P7']['joint_angles']
            expected_coords = [247.55, 239.02, -83.49, 0.5334, 0.0, 0.0]
            
            if p7_config[:4] == expected_coords[:4]:
                print("✅ robot_positions_config.json 中的P7坐标已正确更新")
            else:
                print(f"❌ robot_positions_config.json 中的P7坐标不正确")
                print(f"   期望: {expected_coords[:4]}")
                print(f"   实际: {p7_config[:4]}")
                return False
            
            # 检查pro.py文件
            with open('pro.py', 'r', encoding='utf-8') as f:
                pro_content = f.read()
            
            if "247.55, 239.02, -83.49, 0.5334" in pro_content:
                print("✅ pro.py 中的P7坐标已正确更新")
            else:
                print("❌ pro.py 中的P7坐标未正确更新")
                return False
            
            print("✅ 所有配置文件验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件验证失败: {e}")
            return False
    
    def run_complete_test(self) -> bool:
        """运行完整的P7更新测试"""
        print("=" * 60)
        print("🔄 P7坐标更新完整测试")
        print("=" * 60)
        
        # 1. 验证配置文件
        if not self.verify_config_files():
            print("❌ 配置文件验证失败，请检查更新")
            return False
        
        # 2. 连接机器人
        if not self.connect_robot():
            return False
        
        try:
            # 3. 测试P7位置
            if not self.test_p7_position():
                return False
            
            # 4. 测试坐标移动
            if not self.test_coordinate_movement():
                return False
            
            # 5. 测试抓取序列
            if not self.test_picking_sequence_with_new_p7():
                return False
            
            print("\n🎉 P7坐标更新测试全部通过！")
            print(f"✅ 新P7坐标 (X={self.new_p7_coords[0]}, Y={self.new_p7_coords[1]}, Z={self.new_p7_coords[2]}, R={self.new_p7_coords[3]}) 工作正常")
            print(f"✅ 传送带坐标 {self.conveyor_position} mm 已配置")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试过程出错: {e}")
            return False
            
        finally:
            self.disconnect_robot()


def main():
    """主函数"""
    tester = P7UpdateTester()
    
    print("🔄 P7坐标更新测试工具")
    print("=" * 40)
    print("新P7坐标:")
    print(f"  X: 247.55")
    print(f"  Y: 239.02") 
    print(f"  Z: -83.49")
    print(f"  R: 0.5334")
    print(f"  传送带坐标: 257.46 mm")
    print("=" * 40)
    print("1. 运行完整测试")
    print("2. 仅验证配置文件")
    print("3. 仅测试P7位置")
    print("4. 仅测试坐标移动")
    print("0. 退出")
    print("=" * 40)
    
    try:
        choice = input("\n请选择测试项目 (0-4): ").strip()
        
        if choice == "0":
            print("👋 退出测试")
            return
        elif choice == "1":
            tester.run_complete_test()
        elif choice == "2":
            tester.verify_config_files()
        elif choice == "3":
            if tester.connect_robot():
                tester.test_p7_position()
                tester.disconnect_robot()
        elif choice == "4":
            if tester.connect_robot():
                tester.test_coordinate_movement()
                tester.disconnect_robot()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")


if __name__ == "__main__":
    main()
