1#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机械臂抓取功能测试脚本
专门用于测试和调试机械臂的自动化抓取功能
基于lua代码逻辑重新实现
"""

import socket
import time
import json
import threading
from typing import Tuple, Optional

class RobotPickingTester:
    """机械臂抓取功能测试器"""
    
    def __init__(self):
        # 机器人连接配置
        self.robot_ip = "***********"
        self.dashboard_port = 29999
        self.motion_port = 30003
        
        # 视觉系统配置（与lua代码一致）
        self.vision_ip = "************"
        self.detection1_port = 6005  # 检测区1端口
        self.detection2_port = 6006  # 检测区2端口
        
        # 传送带位置配置（与lua代码一致）
        self.detection1_position = 258.45  # 检测区1位置
        self.detection2_position = 643.34  # 检测区2位置
        
        # 连接状态
        self.dashboard_socket = None
        self.motion_socket = None
        self.detection1_socket = None
        self.detection2_socket = None
        self.is_robot_connected = False
        
        # 从配置文件加载点位信息
        self.load_robot_positions()
        
    def load_robot_positions(self):
        """从配置文件加载机器人点位信息"""
        try:
            with open('robot_positions_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.robot_positions = {}
            for pos_name, pos_data in config['positions'].items():
                self.robot_positions[pos_name] = pos_data['joint_angles']
            
            print(f"✅ 已加载 {len(self.robot_positions)} 个点位配置")
            
            # 显示关键点位
            key_positions = ["P7", "P_Detection1", "P_Detection2", "P_Safe_Height", 
                           "P_Waste_Detection1", "P_Assembly1", "P_Material1_D2"]
            
            print("📍 关键点位信息:")
            for pos in key_positions:
                if pos in self.robot_positions:
                    coords = self.robot_positions[pos]
                    print(f"   {pos}: {coords}")
                else:
                    print(f"   ❌ {pos}: 未找到")
                    
        except Exception as e:
            print(f"❌ 加载点位配置失败: {e}")
            # 使用从point.json文件读取的坐标（关节角度）
            self.robot_positions = {
                "P7": [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0],  # point.json关节角度
                "P_Detection1": [1.9212, 69.2963, 66.6202, -0.3064, 0.0, 0.0],
                "P_Detection2": [-3.0048, 23.4193, 23.358, 39.7952, 0.0, 0.0],
                "P_Safe_Height": [-2.3649, 29.5808, 31.8776, -27.4136, 0.0, 0.0],
                "P_Waste_Detection1": [24.9675, 70.9075, 66.9768, 39.7678, 0.0, 0.0],
                "P_Assembly1": [0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0],  # 默认坐标
                "P_Material1_D2": [-22.3874, 75.56, 76.7475, 74.3033, 0.0, 0.0],
            }
            print("⚠️ 使用默认点位配置")
    
    def send_cmd(self, sock, cmd, log_prefix="CMD") -> bool:
        """发送指令到机器人"""
        try:
            if sock is None:
                print(f"❌ Socket为空，无法发送指令 '{cmd}'")
                return False
            
            time.sleep(0.05)  # 50ms间隔
            full_cmd = cmd + "\n"
            print(f"[{log_prefix}] 发送: {full_cmd.strip()}")
            
            sock.settimeout(5.0)
            sock.sendall(full_cmd.encode('utf-8'))
            response = sock.recv(1024).decode('utf-8').strip()
            print(f"[{log_prefix}] 响应: {response}")
            
            if not response:
                print(f"❌ 指令 '{cmd}' 无响应")
                return False
            
            parts = response.split(',')
            error_id_str = parts[0]
            if error_id_str == '0':
                return True
            else:
                print(f"❌ 指令 '{cmd}' 失败，错误码: {error_id_str}")
                return False
                
        except Exception as e:
            print(f"❌ 发送指令 '{cmd}' 时出错: {e}")
            return False
    
    def connect_robot(self) -> bool:
        """连接机器人"""
        try:
            print(f"🔗 正在连接机器人 {self.robot_ip}...")
            
            # 连接Dashboard端口
            self.dashboard_socket = socket.create_connection((self.robot_ip, self.dashboard_port), timeout=5)
            print("✅ Dashboard连接成功")
            
            # 连接Motion端口
            self.motion_socket = socket.create_connection((self.robot_ip, self.motion_port), timeout=5)
            print("✅ Motion连接成功")
            
            # 使能机器人
            print("🔧 正在使能机器人...")
            if not self.send_cmd(self.dashboard_socket, "EnableRobot()", "DASH"):
                raise ConnectionError("机器人使能失败")
            
            time.sleep(1)  # 等待伺服系统稳定
            self.is_robot_connected = True
            print("✅ 机器人连接并使能成功!")
            return True
            
        except Exception as e:
            print(f"❌ 机器人连接失败: {e}")
            if self.dashboard_socket:
                self.dashboard_socket.close()
            if self.motion_socket:
                self.motion_socket.close()
            return False
    
    def disconnect_robot(self):
        """断开机器人连接"""
        try:
            if self.is_robot_connected:
                print("🔌 正在断开机器人连接...")
                self.send_cmd(self.dashboard_socket, "DisableRobot()", "DASH")
                
            if self.dashboard_socket:
                self.dashboard_socket.close()
            if self.motion_socket:
                self.motion_socket.close()
                
            self.is_robot_connected = False
            print("✅ 机器人已断开连接")
            
        except Exception as e:
            print(f"❌ 断开连接时出错: {e}")
    
    def move_to_position(self, position_name: str) -> bool:
        """移动到指定点位"""
        try:
            if not self.is_robot_connected:
                print("❌ 机器人未连接")
                return False
                
            if position_name not in self.robot_positions:
                print(f"❌ 点位 {position_name} 不存在")
                return False
            
            coords = self.robot_positions[position_name]
            joint_cmd = f"MovJ({{{coords[0]}, {coords[1]}, {coords[2]}, {coords[3]}, {coords[4]}, {coords[5]}}})"
            print(f"🤖 移动到 {position_name}...")
            
            success = self.send_cmd(self.motion_socket, joint_cmd, "MOT")
            if success:
                self.send_cmd(self.dashboard_socket, "Sync()", "DASH")
                print(f"✅ 成功移动到 {position_name}")
            else:
                print(f"❌ 移动到 {position_name} 失败")
            
            return success
            
        except Exception as e:
            print(f"❌ 移动到 {position_name} 时出错: {e}")
            return False
    
    def control_suction(self, state: bool) -> bool:
        """控制吸盘状态"""
        try:
            if not self.is_robot_connected:
                print("❌ 机器人未连接")
                return False
            
            cmd = f"DO(1, {1 if state else 0})"
            action = "开启" if state else "关闭"
            print(f"🔌 {action}吸盘...")
            
            success = self.send_cmd(self.motion_socket, cmd, "MOT")
            if success:
                print(f"✅ 吸盘{action}成功")
                time.sleep(0.2)  # 等待IO状态稳定
            else:
                print(f"❌ 吸盘{action}失败")
            
            return success
            
        except Exception as e:
            print(f"❌ 控制吸盘失败: {e}")
            return False
    
    def control_air_blow(self, state: bool) -> bool:
        """控制喷气状态"""
        try:
            if not self.is_robot_connected:
                print("❌ 机器人未连接")
                return False
            
            cmd = f"DO(2, {1 if state else 0})"
            action = "开启" if state else "关闭"
            print(f"💨 {action}喷气...")
            
            success = self.send_cmd(self.motion_socket, cmd, "MOT")
            if success:
                print(f"✅ 喷气{action}成功")
            else:
                print(f"❌ 喷气{action}失败")
            
            return success
            
        except Exception as e:
            print(f"❌ 控制喷气失败: {e}")
            return False
    
    def test_basic_movements(self) -> bool:
        """测试基本移动功能"""
        print("\n🧪 测试基本移动功能...")
        
        test_positions = ["P7", "P_Detection1", "P_Detection2", "P_Safe_Height"]
        
        for pos in test_positions:
            print(f"\n📍 测试移动到 {pos}...")
            if not self.move_to_position(pos):
                print(f"❌ 移动到 {pos} 失败")
                return False
            time.sleep(1)  # 等待运动完成
        
        print("✅ 基本移动测试完成")
        return True
    
    def test_io_control(self) -> bool:
        """测试IO控制功能"""
        print("\n🧪 测试IO控制功能...")
        
        # 测试吸盘
        print("🔌 测试吸盘控制...")
        if not self.control_suction(True):
            return False
        time.sleep(1)
        if not self.control_suction(False):
            return False
        
        # 测试喷气
        print("💨 测试喷气控制...")
        if not self.control_air_blow(True):
            return False
        time.sleep(1)
        if not self.control_air_blow(False):
            return False
        
        print("✅ IO控制测试完成")
        return True

    def safe_move_to_coordinates(self, x: float, y: float, z: float, r: float) -> bool:
        """安全移动到指定坐标"""
        try:
            if not self.is_robot_connected:
                print("❌ 机器人未连接")
                return False

            cmd = f"MovL({x}, {y}, {z}, {r}, \"SYNC=1\")"
            print(f"🤖 移动到坐标 ({x:.1f}, {y:.1f}, {z:.1f}, {r:.1f})...")

            success = self.send_cmd(self.motion_socket, cmd, "MOT")
            if success:
                self.send_cmd(self.dashboard_socket, "Sync()", "DASH")
                time.sleep(0.1)
                print(f"✅ 成功移动到坐标")
            else:
                print(f"❌ 移动到坐标失败")

            return success

        except Exception as e:
            print(f"❌ 移动到坐标时出错: {e}")
            return False

    def test_pick_material(self, x: float, y: float, z: float, r: float) -> bool:
        """测试物料抓取功能（基于lua代码逻辑）"""
        try:
            print(f"\n🤏 测试抓取物料 - 坐标: ({x:.1f}, {y:.1f}, {z:.1f}, {r:.1f})")

            # 定义抓取点位（基于lua代码）
            pick_pos_safe_z = z + 50  # 安全高度
            pick_pos_z = -184.72      # 实际抓取高度（来自lua代码）

            # 抓取动作序列
            print("1️⃣ 确保吸盘开启...")
            if not self.control_suction(True):
                return False

            print("2️⃣ 移动到安全高度...")
            if not self.safe_move_to_coordinates(x, y, pick_pos_safe_z, r):
                return False
            time.sleep(0.2)

            print("3️⃣ 下降到抓取位置...")
            if not self.safe_move_to_coordinates(x, y, pick_pos_z, r):
                return False
            time.sleep(0.5)  # 等待吸附

            print("4️⃣ 提升到安全高度...")
            if not self.safe_move_to_coordinates(x, y, pick_pos_safe_z, r):
                return False
            time.sleep(0.2)

            print("✅ 物料抓取测试完成")
            return True

        except Exception as e:
            print(f"❌ 抓取测试失败: {e}")
            return False

    def test_place_material(self, target_point: str, r: float) -> bool:
        """测试物料放置功能（基于lua代码逻辑）"""
        try:
            print(f"\n📍 测试放置物料到 {target_point}...")

            if target_point not in self.robot_positions:
                print(f"❌ 目标点位 {target_point} 不存在")
                return False

            # 放置动作序列（基于lua代码）
            print("1️⃣ 移动到目标点上方...")
            # 先移动到目标点位
            if not self.move_to_position(target_point):
                return False
            time.sleep(0.3)

            print("2️⃣ 下降到放置位置...")
            # 这里应该使用MovL下降，但为了测试简化，直接使用点位
            time.sleep(0.2)

            print("3️⃣ 关闭吸盘...")
            if not self.control_suction(False):
                return False
            time.sleep(0.3)

            print("4️⃣ 打开喷气...")
            if not self.control_air_blow(True):
                return False
            time.sleep(0.5)

            print("5️⃣ 关闭喷气...")
            if not self.control_air_blow(False):
                return False

            print("6️⃣ 提升到安全高度...")
            if not self.move_to_position("P_Safe_Height"):
                return False
            time.sleep(0.2)

            print("✅ 物料放置测试完成")
            return True

        except Exception as e:
            print(f"❌ 放置测试失败: {e}")
            return False

    def test_complete_pick_and_place(self) -> bool:
        """测试完整的抓取和放置流程"""
        print("\n🔄 测试完整抓取和放置流程...")

        # 模拟检测到的物料坐标（基于lua代码中的示例）
        test_material = {
            "id": "1",
            "x": 300.0,
            "y": 100.0,
            "z": 20.0,
            "r": 0.0
        }

        print(f"📦 模拟物料: ID={test_material['id']}, 坐标=({test_material['x']}, {test_material['y']}, {test_material['z']}, {test_material['r']})")

        # 1. 移动到拍照位置
        print("\n📸 移动到拍照位置...")
        if not self.move_to_position("P7"):
            return False
        time.sleep(0.5)

        # 2. 执行抓取
        if not self.test_pick_material(test_material['x'], test_material['y'], test_material['z'], test_material['r']):
            return False

        # 3. 执行放置（放到检测区2物料1位置）
        if not self.test_place_material("P_Material1_D2", test_material['r']):
            return False

        # 4. 返回初始位置
        print("\n🏠 返回拍照位置...")
        if not self.move_to_position("P7"):
            return False

        print("✅ 完整抓取和放置流程测试完成")
        return True

    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("=" * 60)
        print("🤖 机械臂抓取功能测试开始")
        print("=" * 60)

        # 连接机器人
        if not self.connect_robot():
            return False

        try:
            # 测试基本移动
            if not self.test_basic_movements():
                return False

            # 测试IO控制
            if not self.test_io_control():
                return False

            # 测试完整抓取流程
            if not self.test_complete_pick_and_place():
                return False

            print("\n🎉 所有测试通过！机械臂抓取功能正常。")
            return True

        except Exception as e:
            print(f"❌ 测试过程中出错: {e}")
            return False

        finally:
            # 断开连接
            self.disconnect_robot()

    def test_single_position(self, position_name: str) -> bool:
        """测试单个点位移动"""
        print(f"\n🎯 测试单个点位: {position_name}")

        if not self.is_robot_connected:
            if not self.connect_robot():
                return False

        try:
            success = self.move_to_position(position_name)
            if success:
                print(f"✅ 点位 {position_name} 测试成功")
            else:
                print(f"❌ 点位 {position_name} 测试失败")
            return success

        except Exception as e:
            print(f"❌ 测试点位 {position_name} 时出错: {e}")
            return False


def main():
    """主函数"""
    tester = RobotPickingTester()

    print("🤖 机械臂抓取功能测试工具")
    print("=" * 40)
    print("1. 运行完整测试")
    print("2. 测试单个点位")
    print("3. 测试基本移动")
    print("4. 测试IO控制")
    print("5. 测试抓取流程")
    print("0. 退出")
    print("=" * 40)

    while True:
        try:
            choice = input("\n请选择测试项目 (0-5): ").strip()

            if choice == "0":
                print("👋 退出测试工具")
                break
            elif choice == "1":
                tester.run_all_tests()
            elif choice == "2":
                pos_name = input("请输入点位名称 (如 P7): ").strip()
                tester.test_single_position(pos_name)
            elif choice == "3":
                if tester.connect_robot():
                    tester.test_basic_movements()
                    tester.disconnect_robot()
            elif choice == "4":
                if tester.connect_robot():
                    tester.test_io_control()
                    tester.disconnect_robot()
            elif choice == "5":
                if tester.connect_robot():
                    tester.test_complete_pick_and_place()
                    tester.disconnect_robot()
            else:
                print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出测试工具")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")


if __name__ == "__main__":
    main()
