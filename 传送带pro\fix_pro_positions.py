#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复pro.py中的点位配置问题
将lua代码中的正确点位信息同步到pro.py中
"""

import json
import re
import shutil
from datetime import datetime

def load_correct_positions():
    """从配置文件加载正确的点位信息"""
    try:
        with open('robot_positions_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        positions = {}
        for pos_name, pos_data in config['positions'].items():
            positions[pos_name] = pos_data['joint_angles']
        
        print(f"✅ 从配置文件加载了 {len(positions)} 个点位")
        return positions, config
        
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return None, None

def backup_pro_file():
    """备份原始pro.py文件"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"pro_backup_{timestamp}.py"
        shutil.copy2("pro.py", backup_name)
        print(f"✅ 已备份原文件为: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ 备份文件失败: {e}")
        return None

def update_pro_positions(positions, config):
    """更新pro.py中的点位配置"""
    try:
        # 读取pro.py文件
        with open('pro.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 构建新的点位字典字符串
        positions_str = "        self.robot_positions = {\n"
        
        # 添加注释
        positions_str += "            # 拍照位置（P7 - 来自point.json.lua，关节角度）\n"
        positions_str += f'            "P7": {positions["P7"]},\n'
        positions_str += f'            "P_Camera_Pos": {positions["P7"]},  # 与P7相同\n\n'
        
        # 添加检测区位置
        positions_str += "            # 检测区位置（来自point.json.lua，关节角度）\n"
        positions_str += f'            "P_Detection1": {positions["P_Detection1"]},\n'
        positions_str += f'            "P_Detection2": {positions["P_Detection2"]},\n\n'
        
        # 添加安全高度位置
        positions_str += "            # 安全高度位置（来自point.json.lua，关节角度）\n"
        positions_str += f'            "P_Safe_Height": {positions["P_Safe_Height"]},\n\n'
        
        # 添加废料位置
        positions_str += "            # 检测区1废料位置（来自point.json.lua，关节角度）\n"
        positions_str += f'            "P_Waste_Detection1": {positions["P_Waste_Detection1"]},\n\n'
        
        # 添加装配区1（废料区）
        positions_str += "            # 装配区1（废料区）（来自point.json.lua，关节角度）\n"
        positions_str += f'            "P_Assembly1": {positions["P_Assembly1"]},\n\n'
        
        # 添加检测区2的6个物料放置点
        positions_str += "            # 检测区2的6个物料放置点（来自point.json.lua，关节角度）\n"
        for i in range(1, 7):
            pos_name = f"P_Material{i}_D2"
            if pos_name in positions:
                positions_str += f'            "{pos_name}": {positions[pos_name]},\n'
        
        positions_str += "\n"
        
        # 添加其他标定点位（如果存在）
        other_positions = ["P1", "P2", "P3", "P4", "P5", "P6", "P9", "InitialPose"]
        positions_str += "            # 其他标定点位（来自point.json.lua，关节角度）\n"
        for pos in other_positions:
            if pos in positions:
                positions_str += f'            "{pos}": {positions[pos]},\n'
        
        positions_str = positions_str.rstrip(',\n') + "\n        }"
        
        # 使用正则表达式替换原有的点位字典
        pattern = r'self\.robot_positions\s*=\s*\{[^}]*\}'
        
        # 查找匹配位置
        match = re.search(pattern, content, re.DOTALL)
        if match:
            # 替换点位字典
            new_content = content[:match.start()] + positions_str + content[match.end():]
            
            # 更新传送带配置参数
            conveyor_settings = config['conveyor_settings']
            vision_settings = config['vision_settings']
            
            # 更新检测区位置配置
            new_content = re.sub(
                r'self\.detection1_position\s*=\s*[\d.]+',
                f'self.detection1_position = {conveyor_settings["detection1_position"]}',
                new_content
            )
            new_content = re.sub(
                r'self\.detection2_position\s*=\s*[\d.]+',
                f'self.detection2_position = {conveyor_settings["detection2_position"]}',
                new_content
            )
            
            # 更新视觉系统配置
            new_content = re.sub(
                r'self\.vision_ip\s*=\s*"[^"]*"',
                f'self.vision_ip = "{vision_settings["ip"]}"',
                new_content
            )
            new_content = re.sub(
                r'self\.detection1_port\s*=\s*\d+',
                f'self.detection1_port = {vision_settings["detection1_port"]}',
                new_content
            )
            new_content = re.sub(
                r'self\.detection2_port\s*=\s*\d+',
                f'self.detection2_port = {vision_settings["detection2_port"]}',
                new_content
            )
            
            # 写入更新后的内容
            with open('pro.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ pro.py文件更新成功")
            return True
        else:
            print("❌ 未找到点位字典定义")
            return False
            
    except Exception as e:
        print(f"❌ 更新pro.py失败: {e}")
        return False

def verify_update():
    """验证更新是否成功"""
    try:
        print("\n🔍 验证更新结果...")
        
        # 尝试导入更新后的模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("pro_updated", "pro.py")
        pro_module = importlib.util.module_from_spec(spec)
        
        # 检查语法是否正确
        with open('pro.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        try:
            compile(content, 'pro.py', 'exec')
            print("✅ 语法检查通过")
        except SyntaxError as e:
            print(f"❌ 语法错误: {e}")
            return False
        
        # 检查关键配置是否存在
        key_checks = [
            'self.robot_positions',
            'self.detection1_position = 258.45',
            'self.detection2_position = 643.34',
            'self.vision_ip = "************"',
            'self.detection1_port = 6005',
            'self.detection2_port = 6006'
        ]
        
        for check in key_checks:
            if check in content:
                print(f"✅ 找到配置: {check}")
            else:
                print(f"❌ 缺少配置: {check}")
                return False
        
        print("✅ 验证通过，更新成功")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def create_integration_summary():
    """创建集成总结报告"""
    try:
        summary = {
            "update_time": datetime.now().isoformat(),
            "description": "将lua代码中的点位信息集成到pro.py中",
            "changes": [
                "更新了robot_positions字典中的所有点位坐标",
                "同步了传送带位置配置（detection1_position, detection2_position）",
                "同步了视觉系统配置（vision_ip, detection1_port, detection2_port）",
                "确保了P7坐标与P_Camera_Pos一致",
                "添加了完整的检测区2物料放置点位（P_Material1_D2到P_Material6_D2）"
            ],
            "key_positions": {
                "P7": "拍照位置（主要）",
                "P_Camera_Pos": "拍照位置（与P7相同）",
                "P_Detection1": "检测区1位置",
                "P_Detection2": "检测区2位置",
                "P_Safe_Height": "安全高度位置",
                "P_Waste_Detection1": "检测区1废料位置",
                "P_Assembly1": "装配区1（废料区）",
                "P_Material1_D2": "检测区2物料1放置点"
            },
            "next_steps": [
                "运行test_robot_picking.py测试机械臂抓取功能",
                "启动pro.py程序测试UI界面",
                "连接机器人并测试各个点位移动",
                "测试自动化分拣流程",
                "验证与视觉系统的通信"
            ]
        }
        
        with open('integration_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print("\n📋 集成总结:")
        print(f"✅ 更新时间: {summary['update_time']}")
        print(f"✅ 描述: {summary['description']}")
        print("\n🔧 主要变更:")
        for change in summary['changes']:
            print(f"   • {change}")
        
        print("\n📍 关键点位:")
        for pos, desc in summary['key_positions'].items():
            print(f"   • {pos}: {desc}")
        
        print("\n🚀 下一步操作:")
        for step in summary['next_steps']:
            print(f"   • {step}")
        
        print(f"\n📄 详细报告已保存到: integration_summary.json")
        
    except Exception as e:
        print(f"❌ 创建总结报告失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 修复pro.py中的点位配置问题")
    print("=" * 60)
    
    # 1. 加载正确的点位信息
    positions, config = load_correct_positions()
    if not positions:
        return False
    
    # 2. 备份原文件
    backup_file = backup_pro_file()
    if not backup_file:
        return False
    
    # 3. 更新pro.py
    if not update_pro_positions(positions, config):
        print(f"❌ 更新失败，可以从备份文件 {backup_file} 恢复")
        return False
    
    # 4. 验证更新
    if not verify_update():
        print(f"❌ 验证失败，可以从备份文件 {backup_file} 恢复")
        return False
    
    # 5. 创建集成总结
    create_integration_summary()
    
    print("\n🎉 点位配置修复完成！")
    print("\n💡 建议下一步操作:")
    print("1. 运行 python test_robot_picking.py 测试机械臂抓取功能")
    print("2. 运行 python pro.py 启动主程序")
    print("3. 在主程序中测试各个点位移动")
    print("4. 测试自动化分拣流程")
    
    return True

if __name__ == "__main__":
    main()
