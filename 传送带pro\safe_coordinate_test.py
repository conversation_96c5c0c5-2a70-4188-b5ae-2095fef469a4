#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全坐标测试工具
用于测试机械臂的安全工作范围，避免关节限位错误
"""

import socket
import time

def send_cmd(sock, cmd):
    """发送指令到机器人"""
    try:
        full_cmd = cmd + "\n"
        print(f"📤 发送: {full_cmd.strip()}")
        
        sock.settimeout(5.0)
        sock.sendall(full_cmd.encode('utf-8'))
        response = sock.recv(1024).decode('utf-8').strip()
        print(f"📥 响应: {response}")
        
        if response and response.split(',')[0] == '0':
            return True, response
        else:
            return False, response
            
    except Exception as e:
        print(f"❌ 发送指令出错: {e}")
        return False, str(e)

def test_safe_coordinates():
    """测试安全坐标范围"""
    robot_ip = "***********"
    dashboard_port = 29999
    motion_port = 30003
    
    print("🛡️ 机械臂安全坐标测试")
    print("=" * 50)
    
    try:
        # 连接机器人
        print("🔗 连接机器人...")
        dashboard_socket = socket.create_connection((robot_ip, dashboard_port), timeout=5)
        motion_socket = socket.create_connection((robot_ip, motion_port), timeout=5)
        print("✅ 机器人连接成功")
        
        # 使能机器人
        print("🔧 使能机器人...")
        success, response = send_cmd(dashboard_socket, "EnableRobot()")
        if success:
            print("✅ 机器人使能成功")
            time.sleep(2)
        else:
            print(f"❌ 机器人使能失败: {response}")
            return False
        
        # 先移动到安全的初始位置
        print("🏠 移动到初始安全位置...")
        safe_init = [0.0, 32.29, 32.85, 0.0, 0.0, 0.0]
        joint_cmd = f"MovJ({{{safe_init[0]}, {safe_init[1]}, {safe_init[2]}, {safe_init[3]}, {safe_init[4]}, {safe_init[5]}}})"
        success, response = send_cmd(motion_socket, joint_cmd)
        if success:
            print("✅ 初始位置移动成功")
            send_cmd(dashboard_socket, "Sync()")
            time.sleep(2)
        else:
            print(f"❌ 初始位置移动失败: {response}")
            return False
        
        # 测试多个安全的拍照位置
        print("\n🎯 测试安全拍照位置...")
        
        safe_positions = [
            ("安全位置1", [0.0, 30.0, 40.0, -25.0, 0.0, 0.0]),
            ("安全位置2", [0.0, 25.0, 35.0, -20.0, 0.0, 0.0]),
            ("安全位置3", [0.0, 35.0, 45.0, -30.0, 0.0, 0.0]),
            ("安全位置4", [10.0, 30.0, 40.0, -25.0, 0.0, 0.0]),
            ("安全位置5", [-10.0, 30.0, 40.0, -25.0, 0.0, 0.0]),
        ]
        
        successful_positions = []
        
        for name, coords in safe_positions:
            print(f"\n🔍 测试 {name}: {coords}")
            joint_cmd = f"MovJ({{{coords[0]}, {coords[1]}, {coords[2]}, {coords[3]}, {coords[4]}, {coords[5]}}})"
            
            success, response = send_cmd(motion_socket, joint_cmd)
            if success:
                print(f"✅ {name} 移动成功")
                
                # 等待运动完成
                sync_success, _ = send_cmd(dashboard_socket, "Sync()")
                if sync_success:
                    print(f"✅ {name} 运动完成")
                    successful_positions.append((name, coords))
                    time.sleep(1)
                else:
                    print(f"⚠️ {name} Sync超时，但移动成功")
                    successful_positions.append((name, coords))
                    time.sleep(2)
            else:
                print(f"❌ {name} 移动失败: {response}")
                if "limit" in response.lower() or "限位" in response:
                    print(f"   原因: 关节限位")
                elif "singular" in response.lower() or "奇异" in response:
                    print(f"   原因: 奇异点")
        
        # 回到安全位置
        print(f"\n🏠 回到初始安全位置...")
        joint_cmd = f"MovJ({{{safe_init[0]}, {safe_init[1]}, {safe_init[2]}, {safe_init[3]}, {safe_init[4]}, {safe_init[5]}}})"
        success, response = send_cmd(motion_socket, joint_cmd)
        if success:
            print("✅ 回到初始位置成功")
            send_cmd(dashboard_socket, "Sync()")
        
        # 关闭连接
        dashboard_socket.close()
        motion_socket.close()
        
        # 总结结果
        print("\n" + "=" * 50)
        print("📊 测试结果总结:")
        print(f"✅ 成功的安全位置 ({len(successful_positions)}):")
        
        for name, coords in successful_positions:
            print(f"   {name}: {coords}")
        
        if successful_positions:
            print(f"\n💡 建议使用的P7坐标:")
            best_position = successful_positions[0]  # 使用第一个成功的位置
            print(f"   {best_position[0]}: {best_position[1]}")
            
            print(f"\n🔧 更新pro.py中的P7坐标:")
            print(f'   "P7": {best_position[1]},')
            print(f'   "P_Camera_Pos": {best_position[1]},')
        else:
            print("❌ 没有找到安全的拍照位置")
        
        return len(successful_positions) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_joint_limits():
    """测试关节限位范围"""
    print("\n🔍 MG400机械臂关节限位参考:")
    print("   J1 (基座): ±360° (通常 ±180°)")
    print("   J2 (大臂): -5° ~ +85°")
    print("   J3 (小臂): -10° ~ +85°")
    print("   J4 (手腕): ±360° (通常 ±180°)")
    print("   J5 (俯仰): ±360° (通常不使用)")
    print("   J6 (旋转): ±360° (通常不使用)")
    
    print("\n💡 安全角度建议:")
    print("   J1: -90° ~ +90°")
    print("   J2: 10° ~ 70°")
    print("   J3: 20° ~ 70°")
    print("   J4: -90° ~ +90°")
    print("   J5: 0°")
    print("   J6: 0°")

def main():
    """主函数"""
    print("🛡️ 机械臂安全坐标测试工具")
    print("用于找到安全的P7拍照位置，避免关节限位错误")
    print("=" * 60)
    
    try:
        # 显示关节限位信息
        test_joint_limits()
        
        # 测试安全坐标
        success = test_safe_coordinates()
        
        if success:
            print("\n🎉 找到了安全的拍照位置！")
            print("💡 现在可以更新pro.py并重新测试")
        else:
            print("\n❌ 未找到合适的安全位置")
            print("💡 建议检查机械臂硬件或调整测试范围")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
