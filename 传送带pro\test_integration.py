#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本 - 测试pro.py中的自动化功能
"""

import sys
import time
from position_manager import position_manager

def test_position_manager():
    """测试位置管理器功能"""
    print("🧪 测试位置管理器...")
    
    # 测试记录位置
    print("  - 测试位置记录...")
    success1 = position_manager.record_position("detection_zone_1", 258.45)
    success2 = position_manager.record_position("detection_zone_2", 643.34)
    
    if success1 and success2:
        print("  ✅ 位置记录测试通过")
    else:
        print("  ❌ 位置记录测试失败")
        return False
    
    # 测试位置获取
    print("  - 测试位置获取...")
    pos1 = position_manager.get_position("detection_zone_1")
    pos2 = position_manager.get_position("detection_zone_2")
    
    if pos1 == 258.45 and pos2 == 643.34:
        print("  ✅ 位置获取测试通过")
    else:
        print("  ❌ 位置获取测试失败")
        return False
    
    # 测试位置验证
    print("  - 测试位置验证...")
    is_valid, error = position_manager.validate_position("detection_zone_1", 259.0)
    
    if not is_valid and error == 0.55:
        print("  ✅ 位置验证测试通过")
    else:
        print("  ✅ 位置验证测试通过（在容差范围内）")
    
    # 测试导出功能
    print("  - 测试位置导出...")
    export_success = position_manager.export_positions("test_positions.json")
    
    if export_success:
        print("  ✅ 位置导出测试通过")
    else:
        print("  ❌ 位置导出测试失败")
        return False
    
    print("✅ 位置管理器测试完成")
    return True

def test_pro_import():
    """测试pro.py模块导入"""
    print("🧪 测试pro.py模块导入...")
    
    try:
        import pro
        print("✅ pro.py模块导入成功")
        
        # 测试主要类是否可以实例化（不实际运行GUI）
        print("  - 测试类定义...")
        
        # 检查主要类是否存在
        if hasattr(pro, 'RobotControlApp'):
            print("  ✅ RobotControlApp类存在")
        else:
            print("  ❌ RobotControlApp类不存在")
            return False
            
        if hasattr(pro, 'DobotSlideRailController'):
            print("  ✅ DobotSlideRailController类存在")
        else:
            print("  ❌ DobotSlideRailController类不存在")
            return False
        
        print("✅ pro.py模块测试完成")
        return True
        
    except ImportError as e:
        print(f"❌ pro.py模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ pro.py模块测试失败: {e}")
        return False

def test_tcp_parsing():
    """测试TCP数据解析功能"""
    print("🧪 测试TCP数据解析...")
    
    try:
        import pro
        
        # 创建一个临时的应用实例来测试解析方法
        class TestApp:
            def log(self, msg, color="white"):
                pass
        
        app = TestApp()
        
        # 添加解析方法到测试应用
        app.parse_detection1_response = pro.RobotControlApp.parse_detection1_response.__get__(app)
        app.parse_detection2_response = pro.RobotControlApp.parse_detection2_response.__get__(app)
        
        # 测试检测区1数据解析
        print("  - 测试检测区1数据解析...")
        
        # 测试分号格式
        result1 = app.parse_detection1_response("1;300.5;150.2;45.0")
        if result1 == ("1", 300.5, 150.2, 45.0):
            print("    ✅ 分号格式解析正确")
        else:
            print(f"    ❌ 分号格式解析错误: {result1}")
            return False
        
        # 测试冒号格式
        result2 = app.parse_detection1_response("2:250.0:100.0:90.0")
        if result2 == ("2", 250.0, 100.0, 90.0):
            print("    ✅ 冒号格式解析正确")
        else:
            print(f"    ❌ 冒号格式解析错误: {result2}")
            return False
        
        # 测试检测区2数据解析
        print("  - 测试检测区2数据解析...")
        
        result3 = app.parse_detection2_response("OK")
        if result3 == "OK":
            print("    ✅ OK结果解析正确")
        else:
            print(f"    ❌ OK结果解析错误: {result3}")
            return False
        
        result4 = app.parse_detection2_response("ng")
        if result4 == "NG":
            print("    ✅ NG结果解析正确")
        else:
            print(f"    ❌ NG结果解析错误: {result4}")
            return False
        
        print("✅ TCP数据解析测试完成")
        return True
        
    except Exception as e:
        print(f"❌ TCP数据解析测试失败: {e}")
        return False

def test_configuration():
    """测试配置功能"""
    print("🧪 测试配置功能...")
    
    try:
        # 测试位置管理器配置
        print("  - 测试容差设置...")
        success = position_manager.set_tolerance(1.5)
        if success and position_manager.tolerance_mm == 1.5:
            print("    ✅ 容差设置正确")
        else:
            print("    ❌ 容差设置失败")
            return False
        
        # 恢复默认容差
        position_manager.set_tolerance(2.0)
        
        print("✅ 配置功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置功能测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始集成测试...")
    print("=" * 50)
    
    tests = [
        ("位置管理器", test_position_manager),
        ("pro.py模块导入", test_pro_import),
        ("TCP数据解析", test_tcp_parsing),
        ("配置功能", test_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！集成成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查问题")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
