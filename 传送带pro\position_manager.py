# =================================================================================
#  位置管理器模块 - Position Manager
# =================================================================================
"""
位置管理器模块，用于管理传送带上各个检测区域和装配区域的位置信息
支持位置记录、验证、导出等功能
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Optional, Tuple, Any


class PositionManager:
    """位置管理器类"""
    
    def __init__(self, config_file: str = "positions_config.json"):
        self.config_file = config_file
        self.tolerance_mm = 2.0  # 位置验证容差（毫米）
        
        # 区域显示名称映射
        self.zone_display_names = {
            "detection_zone_1": "检测区一",
            "detection_zone_2": "检测区二", 
            "assembly_zone_1": "装配区一",
            "assembly_zone_2": "装配区二"
        }
        
        # 位置数据存储
        self.positions = {}
        
        # 加载配置
        self.load_positions()
    
    def load_positions(self) -> bool:
        """从配置文件加载位置数据"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.positions = data.get('positions', {})
                    self.tolerance_mm = data.get('tolerance_mm', 2.0)
                    print(f"✅ 位置配置已从 {self.config_file} 加载")
                    return True
            else:
                print(f"⚠️ 配置文件 {self.config_file} 不存在，使用默认配置")
                self.positions = {}
                return True
        except Exception as e:
            print(f"❌ 加载位置配置失败: {e}")
            self.positions = {}
            return False
    
    def save_positions(self) -> bool:
        """保存位置数据到配置文件"""
        try:
            data = {
                'positions': self.positions,
                'tolerance_mm': self.tolerance_mm,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"✅ 位置配置已保存到 {self.config_file}")
            return True
        except Exception as e:
            print(f"❌ 保存位置配置失败: {e}")
            return False
    
    def record_position(self, zone_key: str, position_mm: float) -> bool:
        """记录指定区域的位置"""
        try:
            if zone_key not in self.zone_display_names:
                print(f"❌ 未知的区域键: {zone_key}")
                return False
            
            self.positions[zone_key] = {
                'position_mm': float(position_mm),
                'record_time': datetime.now().strftime("%H:%M:%S"),
                'record_date': datetime.now().strftime("%Y-%m-%d")
            }
            
            # 自动保存
            return self.save_positions()
            
        except Exception as e:
            print(f"❌ 记录位置失败: {e}")
            return False
    
    def get_position(self, zone_key: str) -> Optional[float]:
        """获取指定区域的位置"""
        if zone_key in self.positions:
            return self.positions[zone_key]['position_mm']
        return None
    
    def get_record_time(self, zone_key: str) -> str:
        """获取指定区域的记录时间"""
        if zone_key in self.positions:
            return self.positions[zone_key].get('record_time', '未知')
        return '未记录'
    
    def is_recorded(self, zone_key: str) -> bool:
        """检查指定区域是否已记录位置"""
        return zone_key in self.positions
    
    def is_all_recorded(self) -> bool:
        """检查是否所有区域都已记录位置"""
        required_zones = list(self.zone_display_names.keys())
        return all(self.is_recorded(zone) for zone in required_zones)
    
    def get_all_recorded_count(self) -> int:
        """获取已记录位置的区域数量"""
        return len(self.positions)
    
    def get_zone_display_name(self, zone_key: str) -> str:
        """获取区域的显示名称"""
        return self.zone_display_names.get(zone_key, zone_key)
    
    def validate_position(self, zone_key: str, actual_position: float) -> Tuple[bool, float]:
        """验证位置精度"""
        expected_position = self.get_position(zone_key)
        if expected_position is None:
            return False, float('inf')
        
        error = abs(actual_position - expected_position)
        is_valid = error <= self.tolerance_mm
        
        return is_valid, error
    
    def clear_all_records(self) -> bool:
        """清除所有位置记录"""
        try:
            self.positions = {}
            return self.save_positions()
        except Exception as e:
            print(f"❌ 清除位置记录失败: {e}")
            return False
    
    def export_positions(self, export_file: str = None) -> bool:
        """导出位置数据"""
        try:
            if export_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                export_file = f"positions_export_{timestamp}.json"
            
            export_data = {
                'export_info': {
                    'export_time': datetime.now().isoformat(),
                    'total_positions': len(self.positions),
                    'tolerance_mm': self.tolerance_mm
                },
                'positions': {}
            }
            
            # 添加位置数据和显示名称
            for zone_key, position_data in self.positions.items():
                export_data['positions'][zone_key] = {
                    'display_name': self.get_zone_display_name(zone_key),
                    'position_mm': position_data['position_mm'],
                    'record_time': position_data['record_time'],
                    'record_date': position_data['record_date']
                }
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 位置数据已导出到 {export_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出位置数据失败: {e}")
            return False
    
    def set_tolerance(self, tolerance_mm: float) -> bool:
        """设置位置验证容差"""
        try:
            if tolerance_mm > 0:
                self.tolerance_mm = float(tolerance_mm)
                return self.save_positions()
            else:
                print("❌ 容差值必须大于0")
                return False
        except Exception as e:
            print(f"❌ 设置容差失败: {e}")
            return False
    
    def get_all_positions_info(self) -> Dict[str, Any]:
        """获取所有位置信息的摘要"""
        info = {
            'total_recorded': len(self.positions),
            'total_required': len(self.zone_display_names),
            'completion_rate': len(self.positions) / len(self.zone_display_names) * 100,
            'tolerance_mm': self.tolerance_mm,
            'positions': {}
        }
        
        for zone_key, zone_name in self.zone_display_names.items():
            if zone_key in self.positions:
                pos_data = self.positions[zone_key]
                info['positions'][zone_key] = {
                    'display_name': zone_name,
                    'position_mm': pos_data['position_mm'],
                    'record_time': pos_data['record_time'],
                    'status': 'recorded'
                }
            else:
                info['positions'][zone_key] = {
                    'display_name': zone_name,
                    'position_mm': None,
                    'record_time': None,
                    'status': 'not_recorded'
                }
        
        return info


# 创建全局实例
position_manager = PositionManager()


# 测试函数
def test_position_manager():
    """测试位置管理器功能"""
    print("🧪 开始测试位置管理器...")
    
    # 测试记录位置
    print("\n1. 测试位置记录...")
    position_manager.record_position("detection_zone_1", 258.45)
    position_manager.record_position("detection_zone_2", 643.34)
    
    # 测试获取位置
    print("\n2. 测试位置获取...")
    pos1 = position_manager.get_position("detection_zone_1")
    pos2 = position_manager.get_position("detection_zone_2")
    print(f"检测区一位置: {pos1} mm")
    print(f"检测区二位置: {pos2} mm")
    
    # 测试位置验证
    print("\n3. 测试位置验证...")
    is_valid, error = position_manager.validate_position("detection_zone_1", 259.0)
    print(f"检测区一验证结果: 有效={is_valid}, 误差={error:.2f} mm")
    
    # 测试导出
    print("\n4. 测试位置导出...")
    position_manager.export_positions("test_export.json")
    
    print("\n✅ 位置管理器测试完成")


if __name__ == "__main__":
    test_position_manager()
