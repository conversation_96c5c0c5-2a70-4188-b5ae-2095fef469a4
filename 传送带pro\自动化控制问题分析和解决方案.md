# 自动化控制问题分析和解决方案

## 🔍 **问题根源分析**

您的观察完全正确！单独控制可以工作，但自动化失败，这确实是pro代码内部控制逻辑的问题。

### **单独控制 vs 自动化控制对比**

#### **✅ 单独控制（工作正常）**：
```python
# XYZ移动 - 使用MoveJog相对移动
def start_jog(self, axis_id):
    cmd = f"MoveJog({axis_id})"  # 简单直接的相对移动
    send_cmd(self.motion_socket, cmd, "MOT")

# 传送带控制 - 使用MovJExt绝对位置
def move_to_absolute_position(self, target_pos):
    cmd = f"MovJExt({target_pos}, {{SYNC=1}})"  # 直接的绝对位置移动
    send_cmd(self.motion_socket, cmd, "MOT")

# IO控制 - 直接DO指令
def control_suction_manual(self, state):
    cmd = f"DO(1, {1 if state else 0})"  # 直接的IO控制
    send_cmd(self.motion_socket, cmd, "MOT")
```

#### **❌ 自动化控制（失败原因）**：
```python
# 问题1: 复杂的坐标转换和多步骤移动
def enhanced_pick_material(self, x, y, z, r):
    # 步骤1: 移动到安全高度
    safe_move_to_coordinates(x, y, z + 50, r)  # MovL指令
    
    # 步骤2: 下降到抓取位置  
    safe_move_to_coordinates(x, y, -184.72, r)  # 固定Z坐标！
    
    # 步骤3: 提升到安全高度
    safe_move_to_coordinates(x, y, z + 50, r)  # MovL指令

# 问题2: MovL vs MovJ的差异
def safe_move_to_coordinates(self, x, y, z, r):
    cmd = f"MovL({x}, {y}, {z}, {r}, \"SYNC=1\")"  # 直线运动
    # vs 单独控制使用的MoveJog（关节运动）

# 问题3: 点位移动使用关节角度
def move_to_position(self, position_name):
    coords = self.robot_positions[position_name]  # 关节角度
    joint_cmd = f"MovJ({{{coords[0]}, {coords[1]}, {coords[2]}, {coords[3]}, {coords[4]}, {coords[5]}}})"
    # 混合使用MovJ和MovL可能导致冲突
```

## 🚨 **具体问题点**

### **1. 坐标系统混乱** ❌
```python
# 问题：混合使用不同的坐标系统
enhanced_pick_material():
    # 使用笛卡尔坐标 (x, y, z, r)
    safe_move_to_coordinates(x, y, z, r)  # MovL指令

move_to_position():
    # 使用关节角度 [J1, J2, J3, J4, J5, J6]
    MovJ({J1, J2, J3, J4, J5, J6})  # MovJ指令
```

### **2. 固定Z坐标问题** ❌
```python
# 问题：使用固定的Z坐标-184.72，可能超出工作空间
pick_pos_z = -184.72  # 来自lua代码，但可能不适用于当前设置
```

### **3. 运动指令冲突** ❌
```python
# 单独控制使用：
MoveJog()  # 关节空间的相对运动，安全可靠

# 自动化使用：
MovL()     # 笛卡尔空间的直线运动，可能遇到奇异点
MovJ()     # 关节空间的绝对运动，需要精确的关节角度
```

### **4. 错误处理不足** ❌
```python
# 单独控制：每个指令独立，失败不影响其他操作
# 自动化：多步骤序列，任何一步失败都会导致整个流程失败
```

## 🔧 **解决方案**

### **方案1: 统一使用相对运动（推荐）** ✅

```python
def simple_pick_material(self, x, y, z, r):
    """简化的抓取逻辑，使用相对运动"""
    try:
        # 1. 开启吸盘
        self.control_suction(True)
        
        # 2. 使用相对运动到达抓取位置（类似手动控制）
        # 先移动XY到目标位置上方
        current_pos = self.get_current_position()  # 需要实现
        
        # 使用MoveJog进行相对移动（安全可靠）
        self.move_relative_xy(x - current_pos[0], y - current_pos[1])
        
        # 下降到抓取高度
        self.move_relative_z(z - current_pos[2])
        
        # 等待吸附
        time.sleep(0.5)
        
        # 提升
        self.move_relative_z(50)  # 相对提升50mm
        
        return True
        
    except Exception as e:
        self.log(f"❌ 简化抓取失败: {e}", "red")
        return False

def move_relative_xy(self, dx, dy):
    """相对XY移动"""
    if dx != 0:
        axis = 1 if dx > 0 else -1
        for _ in range(int(abs(dx))):
            send_cmd(self.motion_socket, f"MoveJog({axis})", "MOT")
            time.sleep(0.1)
    
    if dy != 0:
        axis = 2 if dy > 0 else -2
        for _ in range(int(abs(dy))):
            send_cmd(self.motion_socket, f"MoveJog({axis})", "MOT")
            time.sleep(0.1)
```

### **方案2: 修复现有逻辑** ✅

```python
def fixed_enhanced_pick_material(self, x, y, z, r):
    """修复的增强抓取功能"""
    try:
        # 1. 使用安全的Z坐标，而不是固定值
        safe_z = max(z + 50, -100)  # 确保不超出工作空间
        pick_z = max(z, -150)       # 确保抓取高度安全
        
        # 2. 添加工作空间检查
        if not self.check_workspace_limits(x, y, pick_z):
            self.log(f"❌ 坐标超出工作空间: ({x}, {y}, {pick_z})", "red")
            return False
        
        # 3. 使用更安全的移动方式
        self.control_suction(True)
        
        # 移动到安全高度
        if not self.safe_move_with_retry(x, y, safe_z, r):
            return False
        
        # 下降到抓取位置
        if not self.safe_move_with_retry(x, y, pick_z, r):
            return False
        
        time.sleep(0.5)  # 等待吸附
        
        # 提升到安全高度
        if not self.safe_move_with_retry(x, y, safe_z, r):
            return False
        
        return True
        
    except Exception as e:
        self.log(f"❌ 修复抓取失败: {e}", "red")
        return False

def safe_move_with_retry(self, x, y, z, r, max_retries=3):
    """带重试的安全移动"""
    for attempt in range(max_retries):
        try:
            # 尝试MovL
            cmd = f"MovL({x}, {y}, {z}, {r})"
            success = send_cmd(self.motion_socket, cmd, "MOT")
            
            if success:
                send_cmd(self.dashboard_socket, "Sync()", "DASH")
                time.sleep(0.2)
                return True
            else:
                self.log(f"⚠️ MovL失败，尝试 {attempt + 1}/{max_retries}", "orange")
                
        except Exception as e:
            self.log(f"⚠️ 移动异常: {e}, 尝试 {attempt + 1}/{max_retries}", "orange")
        
        time.sleep(0.5)  # 重试间隔
    
    return False

def check_workspace_limits(self, x, y, z):
    """检查工作空间限制"""
    # MG400工作空间限制（示例）
    if not (-300 <= x <= 300):
        return False
    if not (-300 <= y <= 300):
        return False
    if not (-200 <= z <= 200):
        return False
    return True
```

### **方案3: 使用与手动控制相同的逻辑** ✅

```python
def manual_style_pick_material(self, target_x, target_y, target_z):
    """使用手动控制相同的逻辑进行自动抓取"""
    try:
        # 1. 开启吸盘（与手动控制相同）
        send_cmd(self.motion_socket, "DO(1, 1)", "MOT")
        
        # 2. 获取当前位置（需要实现）
        current_pos = self.get_approximate_position()
        
        # 3. 使用MoveJog移动到目标位置（与手动控制相同）
        # X轴移动
        x_diff = target_x - current_pos[0]
        if abs(x_diff) > 1:  # 1mm精度
            axis = 1 if x_diff > 0 else -1
            duration = abs(x_diff) / 10  # 假设10mm/s速度
            self.jog_for_duration(axis, duration)
        
        # Y轴移动
        y_diff = target_y - current_pos[1]
        if abs(y_diff) > 1:
            axis = 2 if y_diff > 0 else -2
            duration = abs(y_diff) / 10
            self.jog_for_duration(axis, duration)
        
        # Z轴下降
        z_diff = target_z - current_pos[2]
        if z_diff < 0:  # 需要下降
            axis = -3
            duration = abs(z_diff) / 10
            self.jog_for_duration(axis, duration)
        
        # 4. 等待吸附
        time.sleep(0.5)
        
        # 5. Z轴提升
        self.jog_for_duration(3, 5.0)  # 提升50mm
        
        return True
        
    except Exception as e:
        self.log(f"❌ 手动风格抓取失败: {e}", "red")
        return False

def jog_for_duration(self, axis, duration):
    """按指定时间进行jog运动"""
    send_cmd(self.motion_socket, f"MoveJog({axis})", "MOT")
    time.sleep(duration)
    send_cmd(self.motion_socket, f"MoveJog({axis}, 0)", "MOT")  # 停止
```

## 💡 **推荐的修复步骤**

### **立即修复**：
1. **修改enhanced_pick_material函数**，使用安全的Z坐标
2. **添加工作空间检查**，防止超出限位
3. **统一坐标系统**，避免MovL和MovJ混用
4. **增加重试机制**，提高成功率

### **长期优化**：
1. **实现get_current_position函数**，获取实时位置
2. **使用相对运动替代绝对运动**，提高安全性
3. **简化抓取逻辑**，减少失败点
4. **添加更多错误处理**，提高鲁棒性

## 🎯 **结论**

您的分析完全正确！问题确实在于：

1. **坐标系统不一致** - 手动控制使用相对运动，自动化使用绝对坐标
2. **运动指令不同** - 手动使用MoveJog，自动化使用MovL/MovJ
3. **复杂度差异** - 手动控制简单直接，自动化逻辑复杂
4. **错误处理不足** - 自动化缺少足够的错误恢复机制

**建议优先使用方案1（统一使用相对运动），这样可以确保自动化控制与手动控制使用相同的底层逻辑，提高成功率和可靠性。**
