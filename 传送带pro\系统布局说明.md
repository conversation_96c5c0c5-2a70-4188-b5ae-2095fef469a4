# 物料分拣系统布局说明

## 🏗️ 系统整体布局

```
                    拍照位置(P7)
                         |
                         ↓
    ┌─────────────────────────────────────────────────────────┐
    │                   检测区1                                │
    │  ┌─────────────┐                    ┌─────────────┐     │
    │  │   传送带    │                    │  废料放置点  │     │
    │  │  (标定区)   │ ←── 物料识别 ──→   │P_Waste_D1   │     │
    │  └─────────────┘                    └─────────────┘     │
    └─────────────────────────────────────────────────────────┘
                         |
                    传送带移动
                         ↓
    ┌─────────────────────────────────────────────────────────┐
    │                   检测区2                                │
    │  ┌─────────────┐     ┌─────────────────────────────┐    │
    │  │   传送带    │     │      6个物料放置点           │    │
    │  │  (检测区)   │     │  ┌───┬───┬───┬───┬───┬───┐  │    │
    │  └─────────────┘     │  │ 1 │ 2 │ 3 │ 4 │ 5 │ 6 │  │    │
    │                      │  └───┴───┴───┴───┴───┴───┘  │    │
    │                      └─────────────────────────────┘    │
    └─────────────────────────────────────────────────────────┘
                         |
                    最终废料处理
                         ↓
    ┌─────────────────────────────────────────────────────────┐
    │                  装配区1                                 │
    │              (最终废料区)                                │
    │            P_Assembly1                                  │
    └─────────────────────────────────────────────────────────┘
```

## 📍 详细点位说明

### 基础点位
- **P7 (P_Camera_Pos)**: 拍照/准备位置
- **P_Detection1**: 检测区1上方位置
- **P_Detection2**: 检测区2上方位置

### 检测区1点位
- **P_Waste_Detection1**: 检测区1废料放置点
  - 用途：放置未识别物料和干扰物品
  - 触发条件：物料ID = "?"

### 检测区2点位（6个物料放置点）
- **P_Material1_D2**: 检测区2物料1放置点
- **P_Material2_D2**: 检测区2物料2放置点  
- **P_Material3_D2**: 检测区2物料3放置点
- **P_Material4_D2**: 检测区2物料4放置点
- **P_Material5_D2**: 检测区2物料5放置点
- **P_Material6_D2**: 检测区2物料6放置点
  - 用途：放置背面检测OK的物料芯片
  - 触发条件：物料ID = 1-6 且背面检测 = OK

### 最终处理区
- **P_Assembly1**: 装配区1（最终废料区）
  - 用途：放置背面检测NG的物料
  - 触发条件：背面检测 = NG 或检测失败

## 🔄 工作流程详解

### 阶段1：检测区1处理
1. **物料识别**：发送"ok"指令到视觉软件
2. **传送带定位**：移动到检测区1前方（已完成标定板标定）
3. **分拣判断**：
   - **未识别物料(ID="?")**：直接放置到检测区1废料点
   - **物料芯片(ID=1-6)**：吸住物料，进入阶段2

### 阶段2：检测区2处理
1. **传送带移动**：保持吸取状态，移动到检测区2
2. **背面检测**：发送"start"指令进行背面缺陷检测
3. **分拣处理**：
   - **检测OK**：直接放置到装配区2对应ID槽 → 抬高机械臂 → 返回检测区1开始下一轮
   - **检测NG**：抬高机械臂 → 返回检测区1 → 放置到装配区1（废料区）

## 📊 分拣统计

| 物料类型 | 第一阶段处理 | 第二阶段处理 | 安全移动顺序 | 最终去向 |
|----------|-------------|-------------|-------------|----------|
| 未识别物料(ID="?") | 检测区1废料点 | - | - | 检测区1废料点 |
| 物料芯片(ID=1-6) + OK | 进入阶段2 | 背面检测OK | 放置→抬高→返回 | 装配区2对应ID槽 |
| 物料芯片(ID=1-6) + NG | 进入阶段2 | 背面检测NG | 抬高→返回→放置 | 装配区1（废料区） |
| 物料芯片(ID=1-6) + 检测失败 | 进入阶段2 | 检测超时/失败 | 抬高→返回→放置 | 装配区1（废料区） |

## ⚙️ 传送带控制

### 位置参数
- **CONVEYOR_DETECTION1_POS**: 检测区1前方位置
- **CONVEYOR_DETECTION2_POS**: 检测区2前方位置

### 控制逻辑
- 传送带只需要能到达检测区前方即可
- 具体位置数值由实际标定确定
- 使用MovJExt指令精确控制

## 🎯 关键特性

### ✅ 优势
1. **分层处理**：检测区1处理废料，检测区2处理合格品
2. **精确分拣**：6个独立的物料放置点
3. **废料管理**：两级废料处理（检测区1 + 装配区1）
4. **效率优化**：未识别物料直接处理，无需进入第二阶段
5. **安全移动**：机械臂抬高后再平移，避免碰撞

### 🛡️ 安全移动机制
1. **抬高动作**：完成放置后先移动到检测区2上方安全高度
2. **平移移动**：在安全高度下进行水平移动
3. **避免碰撞**：防止机械臂与设备或物料发生碰撞
4. **统一标准**：无论OK还是NG，都执行相同的安全移动流程

### ⚠️ 注意事项
1. **点位示教**：确保所有点位都已正确示教
2. **传送带标定**：检测区1需要完成标定板标定
3. **吸盘控制**：移动到检测区2时保持吸取状态
4. **安全距离**：确保各点位之间有足够的安全距离

## 🔧 配置要求

### 视觉软件配置
- **检测区1(端口6005)**：返回物料ID(1-6或"?")和坐标
- **检测区2(端口6006)**：返回背面检测结果(OK/NG)

### 机械臂配置
- 确保能够到达所有定义的点位
- 吸盘和气泵控制正常
- 运动速度和加速度合理设置

### 传送带配置
- 支持MovJExt指令控制
- 位置精度满足抓取要求
- 速度和加速度参数已调优
