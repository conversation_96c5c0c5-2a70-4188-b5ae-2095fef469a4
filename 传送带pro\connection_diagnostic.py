#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器人连接诊断和修复工具
用于诊断和解决WinError 10053连接问题
"""

import socket
import time
import threading

class RobotConnectionDiagnostic:
    def __init__(self):
        self.robot_ip = "***********"
        self.dashboard_port = 29999
        self.motion_port = 30003
        
    def test_network_connectivity(self):
        """测试网络连通性"""
        print("🔍 测试网络连通性...")
        
        import subprocess
        try:
            # Ping测试
            result = subprocess.run(['ping', '-n', '3', self.robot_ip], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Ping {self.robot_ip} 成功")
                return True
            else:
                print(f"❌ Ping {self.robot_ip} 失败")
                print(f"   输出: {result.stdout}")
                return False
        except Exception as e:
            print(f"❌ Ping测试出错: {e}")
            return False
    
    def test_port_connectivity(self, port, port_name):
        """测试端口连通性"""
        print(f"🔍 测试 {port_name} 端口 {port} 连通性...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            result = sock.connect_ex((self.robot_ip, port))
            sock.close()
            
            if result == 0:
                print(f"✅ {port_name} 端口 {port} 连接成功")
                return True
            else:
                print(f"❌ {port_name} 端口 {port} 连接失败，错误码: {result}")
                return False
        except Exception as e:
            print(f"❌ {port_name} 端口测试出错: {e}")
            return False
    
    def test_robot_communication(self):
        """测试机器人通信"""
        print("🔍 测试机器人通信...")
        
        try:
            # 测试Dashboard连接
            dashboard_socket = socket.create_connection((self.robot_ip, self.dashboard_port), timeout=5)
            print("✅ Dashboard连接建立成功")
            
            # 发送简单指令
            cmd = "RobotMode()\n"
            dashboard_socket.sendall(cmd.encode('utf-8'))
            response = dashboard_socket.recv(1024).decode('utf-8').strip()
            print(f"📥 Dashboard响应: {response}")
            
            dashboard_socket.close()
            
            # 测试Motion连接
            motion_socket = socket.create_connection((self.robot_ip, self.motion_port), timeout=5)
            print("✅ Motion连接建立成功")
            
            motion_socket.close()
            
            return True
            
        except Exception as e:
            print(f"❌ 机器人通信测试失败: {e}")
            return False
    
    def test_continuous_connection(self, duration=30):
        """测试连续连接稳定性"""
        print(f"🔍 测试连续连接稳定性 ({duration}秒)...")
        
        try:
            dashboard_socket = socket.create_connection((self.robot_ip, self.dashboard_port), timeout=5)
            motion_socket = socket.create_connection((self.robot_ip, self.motion_port), timeout=5)
            
            start_time = time.time()
            test_count = 0
            success_count = 0
            
            while time.time() - start_time < duration:
                try:
                    # 发送心跳指令
                    cmd = "RobotMode()\n"
                    dashboard_socket.settimeout(2.0)
                    dashboard_socket.sendall(cmd.encode('utf-8'))
                    response = dashboard_socket.recv(1024).decode('utf-8').strip()
                    
                    test_count += 1
                    if response:
                        success_count += 1
                    
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"⚠️ 连接测试 {test_count} 失败: {e}")
                    test_count += 1
            
            dashboard_socket.close()
            motion_socket.close()
            
            success_rate = (success_count / test_count) * 100 if test_count > 0 else 0
            print(f"📊 连接稳定性测试结果:")
            print(f"   总测试次数: {test_count}")
            print(f"   成功次数: {success_count}")
            print(f"   成功率: {success_rate:.1f}%")
            
            return success_rate > 90
            
        except Exception as e:
            print(f"❌ 连续连接测试失败: {e}")
            return False
    
    def diagnose_connection_issue(self):
        """诊断连接问题"""
        print("🔍 开始诊断机器人连接问题...")
        print("=" * 50)
        
        issues = []
        
        # 1. 网络连通性测试
        if not self.test_network_connectivity():
            issues.append("网络连通性问题")
        
        # 2. 端口连通性测试
        if not self.test_port_connectivity(self.dashboard_port, "Dashboard"):
            issues.append("Dashboard端口连接问题")
        
        if not self.test_port_connectivity(self.motion_port, "Motion"):
            issues.append("Motion端口连接问题")
        
        # 3. 机器人通信测试
        if not self.test_robot_communication():
            issues.append("机器人通信问题")
        
        # 4. 连接稳定性测试
        if not self.test_continuous_connection(10):  # 10秒测试
            issues.append("连接稳定性问题")
        
        print("\n" + "=" * 50)
        print("📋 诊断结果:")
        
        if not issues:
            print("✅ 未发现连接问题")
            print("💡 WinError 10053可能是临时性问题，建议:")
            print("   1. 重新启动pro.py程序")
            print("   2. 检查机器人控制器状态")
            print("   3. 确保机器人已正确使能")
        else:
            print("❌ 发现以下问题:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
            
            print("\n💡 建议解决方案:")
            if "网络连通性问题" in issues:
                print("   - 检查网络连接")
                print("   - 确认机器人IP地址是否正确")
                print("   - 检查防火墙设置")
            
            if "端口连接问题" in issues:
                print("   - 检查机器人控制器是否启动")
                print("   - 确认端口号是否正确")
                print("   - 检查是否有其他程序占用端口")
            
            if "机器人通信问题" in issues:
                print("   - 重启机器人控制器")
                print("   - 检查机器人固件版本")
                print("   - 确认通信协议设置")
            
            if "连接稳定性问题" in issues:
                print("   - 检查网络稳定性")
                print("   - 减少网络负载")
                print("   - 增加重连机制")
    
    def suggest_fixes(self):
        """提供修复建议"""
        print("\n🔧 针对WinError 10053的修复建议:")
        print("1. 立即修复:")
        print("   - 重新启动pro.py程序")
        print("   - 点击'连接机器人'按钮重新连接")
        print("   - 确保机器人控制器正常运行")
        
        print("\n2. 预防措施:")
        print("   - 在pro.py中添加自动重连机制")
        print("   - 增加连接状态监控")
        print("   - 设置合适的超时时间")
        
        print("\n3. 长期解决:")
        print("   - 升级网络设备")
        print("   - 优化网络配置")
        print("   - 定期维护机器人系统")

def main():
    """主函数"""
    print("🔧 机器人连接诊断工具")
    print("用于诊断和解决WinError 10053连接问题")
    print("=" * 50)
    
    diagnostic = RobotConnectionDiagnostic()
    
    try:
        diagnostic.diagnose_connection_issue()
        diagnostic.suggest_fixes()
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断诊断")
    except Exception as e:
        print(f"❌ 诊断工具出错: {e}")

if __name__ == "__main__":
    main()
