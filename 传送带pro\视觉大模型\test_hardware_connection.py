#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DobotVisionStudio 硬件连接测试工具

基于plus.py的通信协议，测试与视觉硬件的连接状态
"""

import socket
import threading
import time
import json
from datetime import datetime

# 硬件连接配置 (来自plus.py)
VISION_HARDWARE_IP = "*************"
VISION_SERVER_PORT = 6005      # 接收视觉硬件数据
VISION_TRIGGER_PORT = 6006     # 发送触发命令
VISION_TRIGGER_CMD = "TRIGGER"

class HardwareConnectionTester:
    """硬件连接测试器"""
    
    def __init__(self):
        self.is_running = True
        self.server_thread = None
        self.received_messages = []
        
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    def test_trigger_connection(self):
        """测试触发端口连接"""
        self.log("测试触发端口连接...")
        
        try:
            with socket.create_connection((VISION_HARDWARE_IP, VISION_TRIGGER_PORT), timeout=5) as s:
                self.log(f"✅ 成功连接到触发端口 {VISION_HARDWARE_IP}:{VISION_TRIGGER_PORT}")
                return True
        except socket.timeout:
            self.log(f"❌ 连接触发端口超时: {VISION_HARDWARE_IP}:{VISION_TRIGGER_PORT}", "ERROR")
            return False
        except ConnectionRefusedError:
            self.log(f"❌ 触发端口连接被拒绝: {VISION_HARDWARE_IP}:{VISION_TRIGGER_PORT}", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ 触发端口连接失败: {e}", "ERROR")
            return False
    
    def test_server_port_binding(self):
        """测试服务端口绑定"""
        self.log("测试服务端口绑定...")
        
        try:
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            test_socket.bind(('0.0.0.0', VISION_SERVER_PORT))
            test_socket.close()
            self.log(f"✅ 服务端口 {VISION_SERVER_PORT} 绑定成功")
            return True
        except OSError as e:
            self.log(f"❌ 服务端口 {VISION_SERVER_PORT} 绑定失败: {e}", "ERROR")
            return False
    
    def start_server_listener(self):
        """启动服务器监听"""
        self.log("启动硬件数据监听服务器...")
        
        self.server_thread = threading.Thread(target=self._server_worker, daemon=True)
        self.server_thread.start()
        
    def _server_worker(self):
        """服务器工作线程"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT))
                s.listen()
                self.log(f"👂 硬件数据监听服务器已启动，端口: {VISION_SERVER_PORT}")
                
                while self.is_running:
                    try:
                        s.settimeout(1.0)  # 设置超时，以便检查is_running
                        conn, addr = s.accept()
                        self.log(f"🤝 硬件设备已连接: {addr}")
                        
                        with conn:
                            while self.is_running:
                                try:
                                    data = conn.recv(1024)
                                    if not data:
                                        break
                                    
                                    message = data.decode('utf-8').strip()
                                    self.received_messages.append({
                                        'timestamp': datetime.now().isoformat(),
                                        'message': message,
                                        'source': addr
                                    })
                                    
                                    self.log(f"📩 收到硬件数据: {message}")
                                    self._parse_hardware_message(message)
                                    
                                except socket.timeout:
                                    continue
                                except Exception as e:
                                    self.log(f"❌ 接收数据错误: {e}", "ERROR")
                                    break
                                    
                    except socket.timeout:
                        continue
                    except Exception as e:
                        if self.is_running:
                            self.log(f"❌ 服务器错误: {e}", "ERROR")
                            
            except Exception as e:
                self.log(f"❌ 服务器启动失败: {e}", "ERROR")
    
    def _parse_hardware_message(self, message):
        """解析硬件消息"""
        try:
            # 消息格式: "X,Y,R;image_path"
            parts = message.split(';')
            
            if len(parts) >= 2:
                coord_data = parts[0]
                image_path = parts[1]
                
                # 解析坐标
                coord_parts = coord_data.split(',')
                if len(coord_parts) >= 2:
                    x = float(coord_parts[0])
                    y = float(coord_parts[1])
                    r = float(coord_parts[2]) if len(coord_parts) > 2 else 0.0
                    
                    self.log(f"📍 解析坐标: X={x:.2f}, Y={y:.2f}, R={r:.2f}")
                    self.log(f"🖼️ 图像路径: {image_path}")
                else:
                    self.log(f"⚠️ 坐标格式错误: {coord_data}", "WARN")
            else:
                self.log(f"⚠️ 消息格式错误: {message}", "WARN")
                
        except Exception as e:
            self.log(f"❌ 解析消息失败: {e}", "ERROR")
    
    def send_trigger_command(self):
        """发送触发命令"""
        self.log("发送触发拍照命令...")
        
        try:
            with socket.create_connection((VISION_HARDWARE_IP, VISION_TRIGGER_PORT), timeout=3) as s:
                cmd_to_send = VISION_TRIGGER_CMD + "\n"
                s.sendall(cmd_to_send.encode('utf-8'))
                self.log("✅ 触发命令发送成功")
                return True
        except Exception as e:
            self.log(f"❌ 发送触发命令失败: {e}", "ERROR")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        self.log("="*60)
        self.log("🚀 开始DobotVisionStudio硬件连接测试")
        self.log("="*60)
        
        # 1. 测试触发端口
        self.log("\n📡 步骤1: 测试触发端口连接")
        trigger_ok = self.test_trigger_connection()
        
        # 2. 测试服务端口绑定
        self.log("\n🔌 步骤2: 测试服务端口绑定")
        server_ok = self.test_server_port_binding()
        
        if not server_ok:
            self.log("❌ 服务端口绑定失败，无法继续测试")
            return False
        
        # 3. 启动监听服务器
        self.log("\n👂 步骤3: 启动数据监听服务器")
        self.start_server_listener()
        time.sleep(1)  # 等待服务器启动
        
        # 4. 发送触发命令
        if trigger_ok:
            self.log("\n📸 步骤4: 发送触发拍照命令")
            self.send_trigger_command()
            
            # 等待接收数据
            self.log("⏳ 等待硬件响应 (10秒)...")
            start_time = time.time()
            while time.time() - start_time < 10:
                if self.received_messages:
                    break
                time.sleep(0.1)
            
            if self.received_messages:
                self.log("✅ 成功接收到硬件响应")
                self._show_received_messages()
            else:
                self.log("⚠️ 未收到硬件响应，可能硬件未连接或配置错误", "WARN")
        
        # 5. 显示测试结果
        self.log("\n📊 测试结果汇总:")
        self.log(f"   触发端口连接: {'✅ 成功' if trigger_ok else '❌ 失败'}")
        self.log(f"   服务端口绑定: {'✅ 成功' if server_ok else '❌ 失败'}")
        self.log(f"   数据接收: {'✅ 成功' if self.received_messages else '❌ 无数据'}")
        
        return trigger_ok and server_ok
    
    def _show_received_messages(self):
        """显示接收到的消息"""
        self.log("\n📋 接收到的消息详情:")
        for i, msg in enumerate(self.received_messages, 1):
            self.log(f"   消息 {i}:")
            self.log(f"     时间: {msg['timestamp']}")
            self.log(f"     来源: {msg['source']}")
            self.log(f"     内容: {msg['message']}")
    
    def interactive_test(self):
        """交互式测试"""
        self.log("🎮 进入交互式测试模式")
        self.log("可用命令:")
        self.log("  trigger - 发送触发命令")
        self.log("  status  - 显示连接状态")
        self.log("  messages - 显示接收到的消息")
        self.log("  quit    - 退出")
        
        # 启动监听服务器
        self.start_server_listener()
        time.sleep(1)
        
        try:
            while True:
                cmd = input("\n请输入命令: ").strip().lower()
                
                if cmd == 'quit':
                    break
                elif cmd == 'trigger':
                    self.send_trigger_command()
                elif cmd == 'status':
                    self.log(f"服务器运行: {self.is_running}")
                    self.log(f"接收消息数: {len(self.received_messages)}")
                elif cmd == 'messages':
                    if self.received_messages:
                        self._show_received_messages()
                    else:
                        self.log("暂无接收到的消息")
                else:
                    self.log("未知命令")
                    
        except KeyboardInterrupt:
            self.log("\n用户中断")
        
        self.stop()
    
    def stop(self):
        """停止测试"""
        self.log("🔄 正在停止测试...")
        self.is_running = False
        
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=2)
        
        self.log("✅ 测试已停止")

def main():
    """主函数"""
    print("🔧 DobotVisionStudio 硬件连接测试工具")
    print("基于plus.py通信协议")
    print("-" * 50)
    
    tester = HardwareConnectionTester()
    
    try:
        print("选择测试模式:")
        print("1. 综合测试 (推荐)")
        print("2. 交互式测试")
        print("3. 仅测试连接")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            tester.run_comprehensive_test()
        elif choice == '2':
            tester.interactive_test()
        elif choice == '3':
            tester.test_trigger_connection()
            tester.test_server_port_binding()
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    finally:
        tester.stop()

if __name__ == "__main__":
    main()
