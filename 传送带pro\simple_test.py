#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

print("🚀 开始简单测试...")

try:
    print("1. 测试position_manager导入...")
    from position_manager import position_manager
    print("   ✅ position_manager导入成功")
    
    print("2. 测试position_manager基本功能...")
    success = position_manager.record_position("test_zone", 100.0)
    if success:
        print("   ✅ 位置记录功能正常")
    else:
        print("   ❌ 位置记录功能失败")
    
    pos = position_manager.get_position("test_zone")
    if pos == 100.0:
        print("   ✅ 位置获取功能正常")
    else:
        print("   ❌ 位置获取功能失败")
    
    print("3. 测试pro模块导入...")
    import pro
    print("   ✅ pro模块导入成功")
    
    print("4. 检查主要类...")
    if hasattr(pro, 'RobotControlApp'):
        print("   ✅ RobotControlApp类存在")
    else:
        print("   ❌ RobotControlApp类不存在")
    
    if hasattr(pro, 'DobotSlideRailController'):
        print("   ✅ DobotSlideRailController类存在")
    else:
        print("   ❌ DobotSlideRailController类不存在")
    
    print("🎉 所有基本测试通过！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 测试错误: {e}")

print("测试完成。")
