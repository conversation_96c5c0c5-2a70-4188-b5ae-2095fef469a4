#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟视觉软件服务端 - 用于测试Python客户端连接
"""

import socket
import threading
import time

class VisionServer:
    def __init__(self, ip="************"):
        self.ip = ip
        self.port_6005 = 6005  # 物料检测端口
        self.port_6006 = 6006  # 缺陷检测端口
        self.running = False
        
    def start_server_6005(self):
        """启动6005端口服务端 - 物料检测"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as server_socket:
                server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                server_socket.bind((self.ip, self.port_6005))
                server_socket.listen(1)
                print(f"🟢 6005端口服务端已启动，监听 {self.ip}:{self.port_6005}")
                
                while self.running:
                    try:
                        server_socket.settimeout(1.0)  # 设置超时，便于检查running状态
                        client_socket, client_address = server_socket.accept()
                        print(f"🤝 6005端口：客户端已连接 {client_address}")
                        
                        with client_socket:
                            while self.running:
                                try:
                                    data = client_socket.recv(1024)
                                    if not data:
                                        break
                                    
                                    message = data.decode('utf-8').strip()
                                    print(f"📥 6005端口收到: {message}")
                                    
                                    if message == "ok":
                                        # 模拟物料检测结果
                                        responses = [
                                            "1;320.53;-0.26;36.50",  # 物料芯片1
                                            "2;280.15;10.45;-15.30", # 物料芯片2
                                            "?;0;0;0",               # 未识别物料
                                            "3;350.20;-5.80;60.75"   # 物料芯片3
                                        ]
                                        
                                        # 循环返回不同的结果
                                        import random
                                        response = random.choice(responses)
                                        
                                        print(f"📤 6005端口发送: {response}")
                                        client_socket.sendall(response.encode('utf-8'))
                                    else:
                                        print(f"⚠️ 6005端口：未知指令 {message}")
                                        
                                except socket.timeout:
                                    continue
                                except Exception as e:
                                    print(f"❌ 6005端口处理客户端数据时出错: {e}")
                                    break
                                    
                        print(f"🔌 6005端口：客户端 {client_address} 已断开")
                        
                    except socket.timeout:
                        continue
                    except Exception as e:
                        if self.running:
                            print(f"❌ 6005端口服务端错误: {e}")
                        break
                        
        except Exception as e:
            print(f"❌ 6005端口服务端启动失败: {e}")
    
    def start_server_6006(self):
        """启动6006端口服务端 - 缺陷检测"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as server_socket:
                server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                server_socket.bind((self.ip, self.port_6006))
                server_socket.listen(1)
                print(f"🟢 6006端口服务端已启动，监听 {self.ip}:{self.port_6006}")
                
                while self.running:
                    try:
                        server_socket.settimeout(1.0)
                        client_socket, client_address = server_socket.accept()
                        print(f"🤝 6006端口：客户端已连接 {client_address}")
                        
                        with client_socket:
                            while self.running:
                                try:
                                    data = client_socket.recv(1024)
                                    if not data:
                                        break
                                    
                                    message = data.decode('utf-8').strip()
                                    print(f"📥 6006端口收到: {message}")
                                    
                                    if message == "start":
                                        # 模拟缺陷检测结果
                                        import random
                                        response = random.choice(["OK", "NG"])
                                        
                                        print(f"📤 6006端口发送: {response}")
                                        client_socket.sendall(response.encode('utf-8'))
                                    else:
                                        print(f"⚠️ 6006端口：未知指令 {message}")
                                        
                                except socket.timeout:
                                    continue
                                except Exception as e:
                                    print(f"❌ 6006端口处理客户端数据时出错: {e}")
                                    break
                                    
                        print(f"🔌 6006端口：客户端 {client_address} 已断开")
                        
                    except socket.timeout:
                        continue
                    except Exception as e:
                        if self.running:
                            print(f"❌ 6006端口服务端错误: {e}")
                        break
                        
        except Exception as e:
            print(f"❌ 6006端口服务端启动失败: {e}")
    
    def start(self):
        """启动服务端"""
        self.running = True
        
        # 启动两个服务端线程
        thread_6005 = threading.Thread(target=self.start_server_6005, daemon=True)
        thread_6006 = threading.Thread(target=self.start_server_6006, daemon=True)
        
        thread_6005.start()
        thread_6006.start()
        
        print(f"🚀 视觉服务端已启动，IP: {self.ip}")
        print("📋 支持的指令:")
        print("   6005端口: 发送'ok' → 返回物料ID和坐标")
        print("   6006端口: 发送'start' → 返回OK/NG")
        print("按 Ctrl+C 停止服务端")
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务端...")
            self.running = False
            time.sleep(2)
            print("✅ 服务端已停止")

def main():
    # 创建并启动视觉服务端
    server = VisionServer("************")
    server.start()

if __name__ == "__main__":
    main()
