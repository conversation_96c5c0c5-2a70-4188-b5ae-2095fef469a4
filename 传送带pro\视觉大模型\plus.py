# =================================================================================
#  视觉系统
# =================================================================================
import customtkinter as ctk
import socket
import threading
import queue
import time
from PIL import Image, ImageTk
import os

# --- 视觉配置 ---
PYTHON_PC_IP = "*************"
VISION_SERVER_PORT = 6005
VISION_TRIGGER_PORT = 6006
VISION_TRIGGER_CMD = "TRIGGER"

class VisionApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("视觉系统")
        self.geometry("900x700")
        self.vision_queue = queue.Queue()

        self.create_widgets()
        self.vision_thread = threading.Thread(target=self.vision_listener_thread, daemon=True)
        self.vision_thread.start()
        self.process_vision_queue()
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(padx=10, pady=10, fill="both", expand=True)

        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

        self.left_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.left_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        # 视觉控制区域
        vision_control_frame = ctk.CTkFrame(self.left_frame)
        vision_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(vision_control_frame, text="视觉控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        ctk.CTkButton(vision_control_frame, text="拍照", command=self.trigger_vision_capture).pack(pady=5, padx=5, fill="x")

        # 右侧显示区域
        self.right_frame = ctk.CTkFrame(self.main_frame)
        self.right_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        ctk.CTkLabel(self.right_frame, text="视觉监控界面", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        self.image_display_label = ctk.CTkLabel(self.right_frame, text="[等待视觉软件发送图像...]", bg_color="grey30", height=300)
        self.image_display_label.pack(pady=10, padx=10, fill="both", expand=True)
        ctk.CTkLabel(self.right_frame, text="信息显示/日志", font=ctk.CTkFont(size=14)).pack()
        self.log_textbox = ctk.CTkTextbox(self.right_frame, state="disabled", height=200)
        self.log_textbox.pack(pady=10, padx=10, fill="both", expand=True)

    def log(self, message, color="white"):
        self.log_textbox.configure(state="normal")
        self.log_textbox.tag_config(f"tag_{color}", foreground=color)
        self.log_textbox.insert("end", f"{message}\n", f"tag_{color}")
        self.log_textbox.configure(state="disabled")
        self.log_textbox.see("end")
    def trigger_vision_capture(self):
        self.log("📸 发送拍照触发指令...", "yellow")
        try:
            with socket.create_connection((PYTHON_PC_IP, VISION_TRIGGER_PORT), timeout=3) as s:
                cmd_to_send = VISION_TRIGGER_CMD + "\n"
                s.sendall(cmd_to_send.encode('utf-8'))
                self.log("✅ 触发指令已发送成功。", "green")
        except socket.timeout:
            self.log(f"❌ 触发失败: 连接视觉软件({PYTHON_PC_IP}:{VISION_TRIGGER_PORT})超时。", "red")
        except Exception as e:
            self.log(f"❌ 触发失败: {e}", "red")
    def process_vision_queue(self):
        try:
            while not self.vision_queue.empty():
                message = self.vision_queue.get_nowait()
                self.log(f"📩 收到视觉数据包: {message}", "cyan")
                parts = message.split(';')
                image_path = parts[-1].strip()
                self.show_image_from_path(image_path)
                if len(parts) >= 2:
                    coord_data = parts[0]
                    try:
                        coord_parts = coord_data.split(',')
                        if len(coord_parts) >= 2:
                            robot_x, robot_y = float(coord_parts[0]), float(coord_parts[1])
                            robot_r = float(coord_parts[2]) if len(coord_parts) > 2 else 0.0
                            self.log(f"📍 检测到坐标: X={robot_x:.1f}, Y={robot_y:.1f}, R={robot_r:.1f}", "green")
                        else:
                            self.log(f"⚠️ 坐标部分格式无法解析: {coord_data}", "orange")
                    except (ValueError, IndexError) as e:
                        self.log(f"❌ 解析坐标数据失败: {e}", "red")
        except queue.Empty:
            pass
        self.after(100, self.process_vision_queue)
    def show_image_from_path(self, image_path):
        max_retries = 5; retry_delay = 0.2
        for attempt in range(max_retries):
            if os.path.exists(image_path):
                try:
                    with Image.open(image_path) as image: image.verify()
                    with Image.open(image_path) as image:
                        image.thumbnail((self.image_display_label.winfo_width(), self.image_display_label.winfo_height()), Image.Resampling.LANCZOS)
                        ctk_image = ImageTk.PhotoImage(image)
                        self.image_display_label.configure(image=ctk_image, text=""); self.image_display_label.image = ctk_image
                        self.log(f"✅ 图像显示成功。(尝试第 {attempt + 1} 次)", "green"); return
                except (IOError, SyntaxError) as e: self.log(f"   - 第 {attempt + 1} 次尝试：文件不完整 ({e})，稍后重试...", "yellow"); time.sleep(retry_delay)
                except PermissionError: self.log(f"   - 第 {attempt + 1} 次尝试：文件被占用，稍后重试...", "yellow"); time.sleep(retry_delay)
                except Exception as e: self.log(f"❌ 显示图像时发生未知错误: {e}", "red"); return
            else: self.log(f"❌ 找不到图像文件: {image_path}", "red"); return
        self.log(f"❌ 图像加载失败：在 {max_retries} 次尝试后依然无法读取文件。", "red")
    def vision_listener_thread(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT))
                s.listen()
                print(f"👂 视觉服务器已启动，正在监听端口 {VISION_SERVER_PORT}...")
                while True:
                    conn, addr = s.accept()
                    with conn:
                        print(f"🤝 视觉软件已连接: {addr}")
                        while True:
                            data = conn.recv(1024)
                            if not data:
                                print("🔌 视觉软件已断开。")
                                break
                            self.vision_queue.put(data.decode('utf-8').strip())
            except OSError as e:
                print(f"❌ 端口 {VISION_SERVER_PORT} 绑定失败: {e}")

    def on_closing(self):
        self.destroy()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = VisionApp()
    app.mainloop()