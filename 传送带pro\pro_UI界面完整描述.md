# pro.py UI界面完整描述

## 🖥️ **整体布局结构**

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           机器人分拣控制系统                                      │
├─────────────────────────────┬───────────────────────────────────────────────────┤
│         左侧控制面板          │                右侧监控面板                        │
│      (可滚动，580px宽)       │              (580px宽)                           │
│                            │                                                   │
│  ┌─ 机器人人机控制           │  ┌─ 监控界面                                      │
│  ├─ 精准滑轨控制           │  ├─ 图像显示区域 (300px高)                       │
│  ├─ 位置记录和验证系统      │  └─ 信息显示/日志 (200px高)                      │
│  ├─ 自动化分拣控制         │                                                   │
│  ├─ 点位管理               │                                                   │
│  ├─ 视觉控制               │                                                   │
│  └─ 连接控制               │                                                   │
└─────────────────────────────┴───────────────────────────────────────────────────┘
```

## 📋 **左侧控制面板详细内容**

### **1. 机器人人机控制** 🤖
```
┌─────────────────────────────────────┐
│        机器人人机控制                │
├─────────────────────────────────────┤
│  [X+]     [X-]                     │
│  [Y+]     [Y-]                     │
│  [Z+]     [Z-]                     │
│  [      回原点      ]               │
│  [    测试P7坐标    ]               │
└─────────────────────────────────────┘
```
**功能**：
- **XYZ轴控制按钮**：按住移动，松开停止
- **回原点按钮**：移动到InitialPose位置
- **测试P7坐标按钮**：测试拍照位置是否可达

### **2. 精准滑轨控制** 🚚
```
┌─────────────────────────────────────┐
│        精准滑轨控制                  │
├─────────────────────────────────────┤
│ 当前位置: 0.00 mm                   │
│ 目标位置: -- mm                     │
│ [████████████████] 进度条            │
│                                     │
│ 步进控制                            │
│ 步进距离(mm): [50    ]              │
│ [◀ 后退]  [前进 ▶]                 │
│                                     │
│ 绝对位置控制                        │
│ 目标位置(mm): [     ] [移动到此位置] │
│ [0mm][200mm][400mm][600mm][850mm]   │
│                                     │
│ 标定与设置                          │
│ [标定零点][检测限位][急停]          │
└─────────────────────────────────────┘
```
**功能**：
- **位置显示**：实时显示当前位置和目标位置
- **进度条**：显示移动进度
- **步进控制**：设定步进距离，前进/后退
- **绝对位置控制**：输入目标位置直接移动
- **快速位置按钮**：一键移动到常用位置
- **标定功能**：零点标定、限位检测、急停

### **3. 位置记录和验证系统** 📍
```
┌─────────────────────────────────────┐
│      位置记录和验证系统              │
├─────────────────────────────────────┤
│ 记录进度: 0/4                       │
│ [████████████████] 进度条            │
│                                     │
│ 检测区域                            │
│ 检测区一: 未记录  [验证][记录检测区一]│
│ 检测区二: 未记录  [验证][记录检测区二]│
│                                     │
│ 装配区域                            │
│ 装配区一: 未记录  [验证][记录装配区一]│
│ 装配区二: 未记录  [验证][记录装配区二]│
│                                     │
│ 批量操作                            │
│ [验证所有位置][清除所有记录][导出位置]│
└─────────────────────────────────────┘
```
**功能**：
- **进度显示**：显示已记录的位置数量
- **区域记录**：记录4个关键区域的传送带位置
- **验证功能**：验证记录的位置是否准确
- **批量操作**：一键验证、清除、导出所有位置

### **4. 自动化分拣控制** 🔄
```
┌─────────────────────────────────────┐
│        自动化分拣控制                │
├─────────────────────────────────────┤
│ 系统状态: 未初始化                   │
│ 循环次数: 0                         │
│                                     │
│ 控制操作                            │
│ [初始化系统][启动自动化][停止自动化] │
│                                     │
│ 参数配置                            │
│ 检测区1位置(mm): [258.45]           │
│ 检测区2位置(mm): [643.34]           │
│ 视觉系统IP: [************]         │
│ 检测区1端口: [6005] 检测区2端口:[6006]│
│ [应用配置]                          │
│ [显示详细状态]                      │
│                                     │
│ TCP通信测试                         │
│ [连接检测区1][连接检测区2]          │
│                                     │
│ 检测区1 (6005端口)                  │
│ 发送指令: [ok    ] [发送到6005]     │
│                                     │
│ 检测区2 (6006端口)                  │
│ 发送指令: [start ] [发送到6006]     │
│                                     │
│ 自定义指令测试                      │
│ 指令:[        ] 端口:[6005▼] [发送] │
│                                     │
│ [连接诊断][Ping测试][机器人诊断]    │
│ [测试IO][检查点位]                  │
│                                     │
│ TCP连接状态                         │
│ 检测区1连接: 未连接                 │
│ 检测区2连接: 未连接                 │
└─────────────────────────────────────┘
```
**功能**：
- **状态监控**：显示系统状态和循环次数
- **控制按钮**：初始化、启动、停止自动化
- **参数配置**：设置检测区位置、IP地址、端口
- **TCP通信测试**：测试与视觉系统的连接
- **诊断工具**：连接诊断、Ping测试、IO测试等

### **5. 点位管理** 📐
```
┌─────────────────────────────────────┐
│            点位管理                  │
├─────────────────────────────────────┤
│ 选择点位: [P7        ▼] [移动到此点]│
│                                     │
│ 关节坐标 (度)                       │
│ J1: [0.0    ]                      │
│ J2: [32.29  ]                      │
│ J3: [32.85  ]                      │
│ J4: [0.0    ]                      │
│ J5: [0.0    ]                      │
│ J6: [0.0    ]                      │
│                                     │
│ [获取当前位置][保存点位]            │
│ [加载配置][保存配置]                │
└─────────────────────────────────────┘
```
**功能**：
- **点位选择**：下拉菜单选择预定义点位
- **坐标编辑**：显示和编辑6个关节角度
- **位置操作**：获取当前位置、保存点位
- **配置管理**：加载和保存点位配置文件

### **6. 视觉控制** 📷
```
┌─────────────────────────────────────┐
│            视觉控制                  │
├─────────────────────────────────────┤
│ [          拍照          ]          │
└─────────────────────────────────────┘
```
**功能**：
- **拍照按钮**：触发视觉系统拍照

### **7. 连接控制** 🔗
```
┌─────────────────────────────────────┐
│ 机器人未连接  [自动抓取] [连接机器人] │
└─────────────────────────────────────┘
```
**功能**：
- **连接状态显示**：显示机器人连接状态
- **自动抓取开关**：启用/禁用自动抓取模式
- **连接按钮**：连接/断开机器人

## 📋 **右侧监控面板详细内容**

### **1. 监控界面标题** 📊
```
┌─────────────────────────────────────┐
│            监控界面                  │
└─────────────────────────────────────┘
```

### **2. 图像显示区域** 🖼️
```
┌─────────────────────────────────────┐
│                                     │
│                                     │
│     [等待视觉软件发送图像...]        │
│                                     │
│                                     │
└─────────────────────────────────────┘
```
**功能**：
- **图像显示**：显示来自视觉系统的实时图像
- **状态提示**：显示等待图像或连接状态
- **高度**：300px，可扩展

### **3. 信息显示/日志** 📝
```
┌─────────────────────────────────────┐
│          信息显示/日志               │
├─────────────────────────────────────┤
│ ✅ 机器人连接成功                   │
│ 🔧 正在初始化自动化系统...          │
│ 🤖 移动到 P7 (关节角度): MovJ({...})│
│ 📡 触发检测区1视觉检测...           │
│ 🚚 移动传送带到检测区1...           │
│ 🔄 开始lua逻辑分拣流程...           │
│ ✅ 分拣循环完成                     │
│                                     │
│ [滚动条显示更多日志...]             │
└─────────────────────────────────────┘
```
**功能**：
- **实时日志**：显示系统运行的详细日志
- **彩色标识**：不同颜色表示不同类型的消息
- **自动滚动**：新消息自动滚动到底部
- **高度**：200px，可扩展

## 🎨 **UI设计特点**

### **颜色方案**：
- **成功消息**：绿色 (green)
- **错误消息**：红色 (red)
- **警告消息**：橙色 (orange)
- **信息消息**：青色 (cyan)
- **状态消息**：白色 (white)

### **按钮状态**：
- **启用**：正常颜色
- **禁用**：灰色 (disabled)
- **危险操作**：红色 (急停、停止)
- **重要操作**：绿色 (启动、连接)

### **布局特点**：
- **响应式设计**：窗口大小变化时自动调整
- **可滚动面板**：左侧面板支持滚动查看所有控件
- **分组管理**：相关功能分组在同一框架内
- **直观操作**：按钮和输入框布局清晰易用

## 💡 **交互逻辑**

### **状态联动**：
- 机器人未连接时，大部分控制按钮禁用
- 自动化未初始化时，启动按钮禁用
- 自动化运行时，停止按钮启用，其他控制禁用

### **实时更新**：
- 传送带位置实时更新
- 自动化状态实时显示
- TCP连接状态实时监控
- 日志信息实时滚动

**这就是pro.py的完整UI界面描述！** 🖥️
