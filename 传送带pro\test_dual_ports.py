#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双端口监听测试脚本
测试6005和6006端口的监听功能
"""

import socket
import time
import threading

def test_send_to_port(port, message, delay=1):
    """向指定端口发送测试消息"""
    try:
        print(f"🔗 连接到端口 {port}...")
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.connect(('localhost', port))
            print(f"✅ 已连接到端口 {port}")
            
            time.sleep(delay)  # 等待连接稳定
            
            print(f"📤 向端口 {port} 发送: {message}")
            s.sendall(message.encode('utf-8'))
            
            time.sleep(2)  # 保持连接一段时间
            print(f"🔌 断开端口 {port} 连接")
            
    except Exception as e:
        print(f"❌ 端口 {port} 测试失败: {e}")

def test_6005_port():
    """测试6005端口 - 物料ID和坐标信息"""
    print("\n🧪 测试6005端口 (物料检测)")
    print("=" * 40)
    
    # 测试不同格式的数据
    test_messages = [
        "1;300.5;150.2;45.0",  # 分号格式
        "2:250.0:100.0:90.0",  # 冒号格式
        "3;400.1;200.3;0.0",   # 另一个分号格式
        "?;0;0;0",             # 未识别物料
        "4:350.5:175.8:30.5"   # 另一个冒号格式
    ]
    
    for i, message in enumerate(test_messages):
        print(f"\n📋 测试 {i+1}: {message}")
        test_send_to_port(6005, message, delay=0.5)
        time.sleep(3)  # 等待处理

def test_6006_port():
    """测试6006端口 - 缺陷检测结果"""
    print("\n🧪 测试6006端口 (缺陷检测)")
    print("=" * 40)
    
    # 测试不同的检测结果
    test_messages = [
        "OK",      # 正常结果
        "NG",      # 缺陷结果
        "ok",      # 小写OK
        "ng",      # 小写NG
        "PASS",    # 其他格式（应该解析失败）
        "FAIL"     # 其他格式（应该解析失败）
    ]
    
    for i, message in enumerate(test_messages):
        print(f"\n📋 测试 {i+1}: {message}")
        test_send_to_port(6006, message, delay=0.5)
        time.sleep(3)  # 等待处理

def test_concurrent_ports():
    """测试并发端口通信"""
    print("\n🧪 测试并发端口通信")
    print("=" * 40)
    
    def send_6005_data():
        time.sleep(1)
        test_send_to_port(6005, "5;320.0;180.0;60.0")
    
    def send_6006_data():
        time.sleep(2)
        test_send_to_port(6006, "OK")
    
    # 启动并发线程
    thread1 = threading.Thread(target=send_6005_data)
    thread2 = threading.Thread(target=send_6006_data)
    
    thread1.start()
    thread2.start()
    
    thread1.join()
    thread2.join()
    
    print("✅ 并发测试完成")

def main():
    """主测试函数"""
    print("🚀 开始双端口监听测试")
    print("请确保pro.py已经启动并正在监听6005和6006端口")
    print("=" * 60)
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    try:
        # 测试6005端口
        test_6005_port()
        
        print("\n" + "=" * 60)
        input("按回车键继续测试6006端口...")
        
        # 测试6006端口
        test_6006_port()
        
        print("\n" + "=" * 60)
        input("按回车键开始并发测试...")
        
        # 测试并发通信
        test_concurrent_ports()
        
        print("\n🎉 所有测试完成！")
        print("请检查pro.py的日志输出，确认数据接收正常。")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
