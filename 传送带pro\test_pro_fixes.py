#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pro.py修复效果
验证机械臂移动和连接问题是否解决
"""

import socket
import time

def test_robot_connection():
    """测试机器人连接"""
    print("🔗 测试机器人连接...")
    
    robot_ip = "***********"
    dashboard_port = 29999
    motion_port = 30003
    
    try:
        # 测试Dashboard连接
        print(f"📡 连接Dashboard端口 {dashboard_port}...")
        dashboard_socket = socket.create_connection((robot_ip, dashboard_port), timeout=5)
        print("✅ Dashboard连接成功")
        
        # 测试Motion端口连接
        print(f"📡 连接Motion端口 {motion_port}...")
        motion_socket = socket.create_connection((robot_ip, motion_port), timeout=5)
        print("✅ Motion连接成功")
        
        # 测试使能
        print("🔧 测试机器人使能...")
        cmd = "EnableRobot()\n"
        dashboard_socket.sendall(cmd.encode('utf-8'))
        response = dashboard_socket.recv(1024).decode('utf-8').strip()
        print(f"📥 使能响应: {response}")
        
        if response.split(',')[0] == '0':
            print("✅ 机器人使能成功")
            
            # 测试P7移动
            print("🤖 测试P7位置移动...")
            p7_coords = [43.995098, 49.889301, 54.612801, -43.461700, 0.0, 0.0]
            joint_cmd = f"MovJ({{{p7_coords[0]}, {p7_coords[1]}, {p7_coords[2]}, {p7_coords[3]}, {p7_coords[4]}, {p7_coords[5]}}})\n"
            
            print(f"📤 发送指令: {joint_cmd.strip()}")
            motion_socket.sendall(joint_cmd.encode('utf-8'))
            response = motion_socket.recv(1024).decode('utf-8').strip()
            print(f"📥 移动响应: {response}")
            
            if response.split(',')[0] == '0':
                print("✅ P7移动指令成功")
                
                # 等待运动完成
                print("⏳ 等待运动完成...")
                sync_cmd = "Sync()\n"
                dashboard_socket.sendall(sync_cmd.encode('utf-8'))
                sync_response = dashboard_socket.recv(1024).decode('utf-8').strip()
                print(f"📥 同步响应: {sync_response}")
                
                print("✅ P7位置测试完成")
            else:
                print(f"❌ P7移动失败: {response}")
        else:
            print(f"❌ 机器人使能失败: {response}")
        
        # 断开连接
        dashboard_socket.close()
        motion_socket.close()
        print("🔌 连接已断开")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_coordinate_formats():
    """测试坐标格式"""
    print("\n🧪 测试坐标格式...")
    
    # 测试P7坐标
    p7_joint = [43.995098, 49.889301, 54.612801, -43.461700, 0.0, 0.0]
    p7_cartesian = [247.55, 239.02, -83.49, 0.5334]
    
    print(f"📍 P7关节角度: {p7_joint}")
    print(f"📍 P7笛卡尔坐标: {p7_cartesian}")
    
    # 生成MovJ指令
    joint_cmd = f"MovJ({{{p7_joint[0]}, {p7_joint[1]}, {p7_joint[2]}, {p7_joint[3]}, {p7_joint[4]}, {p7_joint[5]}}})"
    print(f"🤖 关节移动指令: {joint_cmd}")
    
    # 生成MovL指令
    cartesian_cmd = f"MovL({p7_cartesian[0]}, {p7_cartesian[1]}, {p7_cartesian[2]}, {p7_cartesian[3]})"
    print(f"🤖 笛卡尔移动指令: {cartesian_cmd}")
    
    print("✅ 坐标格式测试完成")

def test_vision_data_parsing():
    """测试视觉数据解析"""
    print("\n🔍 测试视觉数据解析...")
    
    test_data = [
        "1,300.0,100.0,0.0",      # 逗号分隔
        "1;300.0;100.0;0.0",      # 分号分隔
        "1:300.0:100.0:0.0",      # 冒号分隔
        "1 300.0 100.0 0.0",      # 空格分隔
    ]
    
    for data in test_data:
        print(f"📥 测试数据: '{data}'")
        
        # 模拟解析逻辑
        try:
            if ',' in data:
                parts = data.split(',')
                separator = "逗号"
            elif ';' in data:
                parts = data.split(';')
                separator = "分号"
            elif ':' in data:
                parts = data.split(':')
                separator = "冒号"
            else:
                parts = data.split()
                separator = "空格"
            
            if len(parts) >= 4:
                material_id = parts[0].strip()
                x = float(parts[1])
                y = float(parts[2])
                r = float(parts[3])
                print(f"✅ 解析成功({separator}格式) - ID:{material_id}, X:{x:.2f}, Y:{y:.2f}, R:{r:.2f}")
            else:
                print(f"❌ 数据格式错误: 字段不足")
                
        except Exception as e:
            print(f"❌ 解析失败: {e}")
    
    print("✅ 视觉数据解析测试完成")

def main():
    """主函数"""
    print("🧪 pro.py修复效果测试")
    print("=" * 50)
    
    print("测试项目:")
    print("1. 机器人连接和P7移动测试")
    print("2. 坐标格式测试")
    print("3. 视觉数据解析测试")
    print("4. 全部测试")
    print("0. 退出")
    
    try:
        choice = input("\n请选择测试项目 (0-4): ").strip()
        
        if choice == "0":
            print("👋 退出测试")
            return
        elif choice == "1":
            test_robot_connection()
        elif choice == "2":
            test_coordinate_formats()
        elif choice == "3":
            test_vision_data_parsing()
        elif choice == "4":
            print("🚀 运行全部测试...\n")
            test_coordinate_formats()
            test_vision_data_parsing()
            test_robot_connection()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
