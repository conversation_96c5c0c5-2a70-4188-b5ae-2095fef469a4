# 物料分拣系统配置说明

## 系统概述
本系统实现了两阶段检测的物料分拣流程：
1. 检测区1：物料缺陷检测（发送"ok"指令）
2. 传送带控制：移动到指定位置
3. 机械臂抓取：从检测区1抓取物料
4. 检测区2：背面缺陷检测（发送"start"指令）
5. 分拣放置：根据OK/NG结果分拣到装配区1或2

## 需要配置的参数

### 1. 传送带控制参数（global.lua）
```lua
-- 需要根据实际硬件调整以下参数：
local conveyor_detection1_pos = 0    -- 检测区1位置
local conveyor_detection2_pos = 1000 -- 检测区2位置
local conveyor_current_pos = 0       -- 当前传送带位置
```

**注意：** 
- 传送带控制函数中的DO端口号需要根据实际接线调整
- 移动时间计算公式需要根据实际传送带速度调整

### 2. 点位坐标（point.json）
需要在机器人上示教以下点位的实际坐标：

- **P_Detection1**: 检测区1上方位置
- **P_Detection2**: 检测区2上方位置  
- **P_Assembly1**: 装配区1（放置NG物料）
- **P_Assembly2**: 装配区2（放置OK物料）
- **P7 (P_Camera_Pos)**: 拍照/准备位置

### 3. 视觉系统通信协议
确保视觉系统能够：
- 接收"ok"指令并返回格式：`ID;状态;X;Y;角度`
- 接收"start"指令并返回：`OK` 或 `NG`

### 4. IO端口配置
- **DO(1, x)**: 吸盘控制（1=开启，0=关闭）
- **DO(2, x)**: 气泵控制（1=开启，0=关闭）
- **DO(3, x)**: 传送带控制（需要根据实际接线调整）

## 工作流程详解

### 正常物料流程：
1. 发送"ok" → 视觉检测区1 → 获取坐标
2. 传送带移动到检测区1
3. 机械臂抓取物料
4. 传送带移动到检测区2
5. 发送"start" → 视觉检测区2 → 获取OK/NG
6. 根据结果分拣到装配区1(NG)或装配区2(OK)

### 缺陷物料流程：
1. 发送"ok" → 视觉检测区1 → 检测到缺陷(ID=0)
2. 直接分拣到装配区1，跳过检测区2

## 调试建议

1. **先运行test_functions.lua**测试各个功能模块
2. **逐步调试**：
   - 先测试TCP通信
   - 再测试传送带控制
   - 然后测试机械臂移动
   - 最后测试完整流程

3. **关键检查点**：
   - 视觉系统返回数据格式是否正确
   - 传送带位置是否准确
   - 机械臂点位是否安全
   - IO端口控制是否正常

## 安全注意事项

1. 首次运行前请确保所有点位都已正确示教
2. 检查机械臂运动路径是否安全
3. 确认传送带控制逻辑正确
4. 测试时建议降低机械臂运动速度

## 故障排除

- **TCP连接失败**：检查IP地址和端口号
- **传送带不动**：检查DO端口配置和接线
- **视觉数据解析失败**：检查数据格式和分隔符
- **机械臂运动异常**：检查点位坐标和工具坐标系
