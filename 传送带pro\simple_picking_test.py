#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的机械臂抓取功能测试
专门用于调试机械臂无法进行自动化抓取的问题
"""

import socket
import time
import json

class SimplePickingTest:
    """简化的机械臂抓取测试器"""
    
    def __init__(self):
        # 机器人连接配置
        self.robot_ip = "***********"
        self.dashboard_port = 29999
        self.motion_port = 30003
        
        # 连接状态
        self.dashboard_socket = None
        self.motion_socket = None
        self.is_connected = False
        
        # 从point.json文件读取的点位信息（关节角度）
        self.robot_positions = {
            "P7": [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0],  # point.json关节角度
            "P_Camera_Pos": [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0],
            "P_Detection1": [1.9212, 69.2963, 66.6202, -0.3064, 0.0, 0.0],
            "P_Detection2": [-3.0048, 23.4193, 23.358, 39.7952, 0.0, 0.0],
            "P_Safe_Height": [-2.3649, 29.5808, 31.8776, -27.4136, 0.0, 0.0],
            "P_Waste_Detection1": [24.9675, 70.9075, 66.9768, 39.7678, 0.0, 0.0],
            "P_Assembly1": [0.0, 32.2659, 32.5935, 0.0, 0.0, 0.0],  # 默认坐标
            "P_Material1_D2": [-22.3874, 75.56, 76.7475, 74.3033, 0.0, 0.0]
        }
    
    def send_cmd(self, sock, cmd) -> bool:
        """发送指令到机器人"""
        try:
            if sock is None:
                print(f"❌ Socket为空")
                return False
            
            full_cmd = cmd + "\n"
            print(f"发送: {full_cmd.strip()}")
            
            sock.settimeout(5.0)
            sock.sendall(full_cmd.encode('utf-8'))
            response = sock.recv(1024).decode('utf-8').strip()
            print(f"响应: {response}")
            
            if response and response.split(',')[0] == '0':
                return True
            else:
                print(f"❌ 指令失败: {response}")
                return False
                
        except Exception as e:
            print(f"❌ 发送指令出错: {e}")
            return False
    
    def connect_robot(self) -> bool:
        """连接机器人"""
        try:
            print(f"🔗 连接机器人 {self.robot_ip}...")
            
            self.dashboard_socket = socket.create_connection((self.robot_ip, self.dashboard_port), timeout=5)
            self.motion_socket = socket.create_connection((self.robot_ip, self.motion_port), timeout=5)
            
            print("🔧 使能机器人...")
            if not self.send_cmd(self.dashboard_socket, "EnableRobot()"):
                raise ConnectionError("机器人使能失败")
            
            time.sleep(1)
            self.is_connected = True
            print("✅ 机器人连接成功!")
            return True
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect_robot(self):
        """断开机器人连接"""
        try:
            if self.is_connected:
                print("🔌 断开机器人连接...")
                self.send_cmd(self.dashboard_socket, "DisableRobot()")
                
            if self.dashboard_socket:
                self.dashboard_socket.close()
            if self.motion_socket:
                self.motion_socket.close()
                
            self.is_connected = False
            print("✅ 机器人已断开")
            
        except Exception as e:
            print(f"❌ 断开连接出错: {e}")
    
    def move_to_position(self, position_name: str) -> bool:
        """移动到指定点位"""
        try:
            if not self.is_connected:
                print("❌ 机器人未连接")
                return False
                
            if position_name not in self.robot_positions:
                print(f"❌ 点位 {position_name} 不存在")
                return False
            
            coords = self.robot_positions[position_name]
            joint_cmd = f"MovJ({{{coords[0]}, {coords[1]}, {coords[2]}, {coords[3]}, {coords[4]}, {coords[5]}}})"
            print(f"🤖 移动到 {position_name}...")
            
            success = self.send_cmd(self.motion_socket, joint_cmd)
            if success:
                self.send_cmd(self.dashboard_socket, "Sync()")
                print(f"✅ 成功移动到 {position_name}")
                time.sleep(0.5)  # 等待运动完成
            else:
                print(f"❌ 移动到 {position_name} 失败")
            
            return success
            
        except Exception as e:
            print(f"❌ 移动出错: {e}")
            return False
    
    def control_suction(self, state: bool) -> bool:
        """控制吸盘"""
        try:
            cmd = f"DO(1, {1 if state else 0})"
            action = "开启" if state else "关闭"
            print(f"🔌 {action}吸盘...")
            
            success = self.send_cmd(self.motion_socket, cmd)
            if success:
                print(f"✅ 吸盘{action}成功")
                time.sleep(0.2)
            else:
                print(f"❌ 吸盘{action}失败")
            
            return success
            
        except Exception as e:
            print(f"❌ 控制吸盘出错: {e}")
            return False
    
    def test_basic_positions(self) -> bool:
        """测试基本点位移动"""
        print("\n🧪 测试基本点位移动...")
        
        test_positions = ["P7", "P_Detection1", "P_Safe_Height"]
        
        for pos in test_positions:
            print(f"\n📍 测试移动到 {pos}...")
            if not self.move_to_position(pos):
                print(f"❌ 移动到 {pos} 失败")
                return False
            time.sleep(1)
        
        print("✅ 基本点位移动测试完成")
        return True
    
    def test_io_functions(self) -> bool:
        """测试IO功能"""
        print("\n🧪 测试IO功能...")
        
        # 测试吸盘
        print("🔌 测试吸盘...")
        if not self.control_suction(True):
            return False
        time.sleep(1)
        if not self.control_suction(False):
            return False
        
        print("✅ IO功能测试完成")
        return True
    
    def test_pick_sequence(self) -> bool:
        """测试抓取序列（基于lua代码逻辑）"""
        print("\n🧪 测试抓取序列...")
        
        try:
            # 1. 移动到拍照位置
            print("1️⃣ 移动到拍照位置...")
            if not self.move_to_position("P7"):
                return False
            
            # 2. 模拟检测到物料，移动到检测区1
            print("2️⃣ 移动到检测区1...")
            if not self.move_to_position("P_Detection1"):
                return False
            
            # 3. 开启吸盘准备抓取
            print("3️⃣ 开启吸盘...")
            if not self.control_suction(True):
                return False
            
            # 4. 模拟抓取动作（这里简化，实际应该有坐标移动）
            print("4️⃣ 模拟抓取动作...")
            time.sleep(1)  # 模拟抓取时间
            
            # 5. 移动到安全高度
            print("5️⃣ 移动到安全高度...")
            if not self.move_to_position("P_Safe_Height"):
                return False
            
            # 6. 移动到检测区2
            print("6️⃣ 移动到检测区2...")
            if not self.move_to_position("P_Detection2"):
                return False
            
            # 7. 移动到放置位置
            print("7️⃣ 移动到放置位置...")
            if not self.move_to_position("P_Material1_D2"):
                return False
            
            # 8. 关闭吸盘
            print("8️⃣ 关闭吸盘...")
            if not self.control_suction(False):
                return False
            
            # 9. 返回安全位置
            print("9️⃣ 返回安全位置...")
            if not self.move_to_position("P_Safe_Height"):
                return False
            
            print("✅ 抓取序列测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 抓取序列测试失败: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("=" * 50)
        print("🤖 简化机械臂抓取功能测试")
        print("=" * 50)
        
        if not self.connect_robot():
            return False
        
        try:
            # 测试基本移动
            if not self.test_basic_positions():
                return False
            
            # 测试IO功能
            if not self.test_io_functions():
                return False
            
            # 测试抓取序列
            if not self.test_pick_sequence():
                return False
            
            print("\n🎉 所有测试通过！机械臂抓取功能正常。")
            print("\n💡 下一步建议:")
            print("1. 在pro.py中测试自动化流程")
            print("2. 连接视觉系统进行完整测试")
            print("3. 验证传送带控制功能")
            return True
            
        except Exception as e:
            print(f"❌ 测试过程出错: {e}")
            return False
            
        finally:
            self.disconnect_robot()


def main():
    """主函数"""
    tester = SimplePickingTest()
    
    print("选择测试项目:")
    print("1. 运行完整测试")
    print("2. 仅测试连接")
    print("3. 仅测试点位移动")
    print("4. 仅测试IO功能")
    print("5. 仅测试抓取序列")
    print("0. 退出")
    
    try:
        choice = input("\n请选择 (0-5): ").strip()
        
        if choice == "0":
            print("👋 退出")
            return
        elif choice == "1":
            tester.run_all_tests()
        elif choice == "2":
            if tester.connect_robot():
                print("✅ 连接测试成功")
                tester.disconnect_robot()
            else:
                print("❌ 连接测试失败")
        elif choice == "3":
            if tester.connect_robot():
                tester.test_basic_positions()
                tester.disconnect_robot()
        elif choice == "4":
            if tester.connect_robot():
                tester.test_io_functions()
                tester.disconnect_robot()
        elif choice == "5":
            if tester.connect_robot():
                tester.test_pick_sequence()
                tester.disconnect_robot()
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")


if __name__ == "__main__":
    main()
