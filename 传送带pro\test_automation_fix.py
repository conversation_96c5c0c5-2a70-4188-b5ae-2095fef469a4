#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动化修复效果
验证简化后的自动化流程是否能正常工作
"""

import socket
import time

def test_simple_robot_movement():
    """测试简化的机器人移动"""
    print("🧪 测试简化的机器人移动...")
    
    robot_ip = "***********"
    dashboard_port = 29999
    motion_port = 30003
    
    try:
        # 连接机器人
        print("🔗 连接机器人...")
        dashboard_socket = socket.create_connection((robot_ip, dashboard_port), timeout=5)
        motion_socket = socket.create_connection((robot_ip, motion_port), timeout=5)
        
        # 使能机器人
        print("🔧 使能机器人...")
        cmd = "EnableRobot()\n"
        dashboard_socket.sendall(cmd.encode('utf-8'))
        response = dashboard_socket.recv(1024).decode('utf-8').strip()
        print(f"使能响应: {response}")
        
        if response.split(',')[0] != '0':
            print(f"❌ 机器人使能失败: {response}")
            return False
        
        time.sleep(1)  # 等待使能完成
        
        # 测试P7移动（使用point.json关节角度）
        print("🤖 测试P7移动...")
        p7_coords = [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0]
        joint_cmd = f"MovJ({{{p7_coords[0]}, {p7_coords[1]}, {p7_coords[2]}, {p7_coords[3]}, {p7_coords[4]}, {p7_coords[5]}}})\n"
        
        print(f"发送指令: {joint_cmd.strip()}")
        motion_socket.sendall(joint_cmd.encode('utf-8'))
        response = motion_socket.recv(1024).decode('utf-8').strip()
        print(f"移动响应: {response}")
        
        if response.split(',')[0] == '0':
            print("✅ P7移动成功")
            time.sleep(2)  # 等待运动完成
            
            # 测试其他关键点位
            test_positions = {
                "P_Detection1": [1.9212, 69.2963, 66.6202, -0.3064, 0.0, 0.0],
                "P_Safe_Height": [-2.3649, 29.5808, 31.8776, -27.4136, 0.0, 0.0]
            }
            
            for pos_name, coords in test_positions.items():
                print(f"🤖 测试 {pos_name}...")
                joint_cmd = f"MovJ({{{coords[0]}, {coords[1]}, {coords[2]}, {coords[3]}, {coords[4]}, {coords[5]}}})\n"
                motion_socket.sendall(joint_cmd.encode('utf-8'))
                response = motion_socket.recv(1024).decode('utf-8').strip()
                
                if response.split(',')[0] == '0':
                    print(f"✅ {pos_name} 移动成功")
                    time.sleep(1)
                else:
                    print(f"❌ {pos_name} 移动失败: {response}")
            
            print("✅ 所有关键点位测试完成")
            
        else:
            print(f"❌ P7移动失败: {response}")
            return False
        
        # 测试IO控制
        print("🔌 测试IO控制...")
        
        # 测试吸盘
        print("测试吸盘开启...")
        suction_cmd = "DO(1, 1)\n"
        motion_socket.sendall(suction_cmd.encode('utf-8'))
        response = motion_socket.recv(1024).decode('utf-8').strip()
        print(f"吸盘开启响应: {response}")
        
        time.sleep(1)
        
        print("测试吸盘关闭...")
        suction_cmd = "DO(1, 0)\n"
        motion_socket.sendall(suction_cmd.encode('utf-8'))
        response = motion_socket.recv(1024).decode('utf-8').strip()
        print(f"吸盘关闭响应: {response}")
        
        # 测试喷气
        print("测试喷气开启...")
        air_cmd = "DO(2, 1)\n"
        motion_socket.sendall(air_cmd.encode('utf-8'))
        response = motion_socket.recv(1024).decode('utf-8').strip()
        print(f"喷气开启响应: {response}")
        
        time.sleep(1)
        
        print("测试喷气关闭...")
        air_cmd = "DO(2, 0)\n"
        motion_socket.sendall(air_cmd.encode('utf-8'))
        response = motion_socket.recv(1024).decode('utf-8').strip()
        print(f"喷气关闭响应: {response}")
        
        print("✅ IO控制测试完成")
        
        # 断开连接
        dashboard_socket.close()
        motion_socket.close()
        print("🔌 连接已断开")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_vision_simulation():
    """测试视觉系统模拟"""
    print("\n🔍 测试视觉系统模拟...")
    
    # 模拟检测区1数据
    test_data = "1,300.0,100.0,0.0"
    print(f"模拟检测区1数据: {test_data}")
    
    # 解析测试
    try:
        parts = test_data.split(',')
        if len(parts) >= 4:
            material_id = parts[0].strip()
            x = float(parts[1])
            y = float(parts[2])
            r = float(parts[3])
            print(f"✅ 解析成功 - ID:{material_id}, X:{x:.2f}, Y:{y:.2f}, R:{r:.2f}")
        else:
            print("❌ 数据格式错误")
            return False
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return False
    
    # 模拟检测区2数据
    test_result = "OK"
    print(f"模拟检测区2数据: {test_result}")
    
    if test_result.upper().strip() in ["OK", "NG"]:
        print(f"✅ 检测区2数据有效: {test_result}")
    else:
        print("❌ 检测区2数据无效")
        return False
    
    print("✅ 视觉系统模拟测试完成")
    return True

def main():
    """主函数"""
    print("🧪 自动化修复效果测试")
    print("=" * 50)
    
    print("修复内容:")
    print("✅ 移除了有问题的Tool(0)和User(0)配置")
    print("✅ 简化了MovJ指令，直接使用关节角度")
    print("✅ 移除了多余的Sync()调用")
    print("✅ 优化了错误处理和日志输出")
    print("✅ 保持了XYZ按钮和传送带的正常功能")
    print()
    
    print("测试项目:")
    print("1. 机器人移动和IO控制测试")
    print("2. 视觉系统模拟测试")
    print("3. 全部测试")
    print("0. 退出")
    
    try:
        choice = input("\n请选择测试项目 (0-3): ").strip()
        
        if choice == "0":
            print("👋 退出测试")
            return
        elif choice == "1":
            test_simple_robot_movement()
        elif choice == "2":
            test_vision_simulation()
        elif choice == "3":
            print("🚀 运行全部测试...\n")
            vision_ok = test_vision_simulation()
            robot_ok = test_simple_robot_movement()
            
            print("\n" + "="*50)
            print("📊 测试结果总结:")
            print(f"   视觉系统模拟: {'✅ 通过' if vision_ok else '❌ 失败'}")
            print(f"   机器人控制: {'✅ 通过' if robot_ok else '❌ 失败'}")
            
            if vision_ok and robot_ok:
                print("\n🎉 所有测试通过！pro.py应该可以正常工作了。")
                print("\n💡 下一步:")
                print("1. 启动pro.py程序")
                print("2. 连接机器人")
                print("3. 初始化自动化系统")
                print("4. 启动自动化流程")
                print("5. 使用网络通讯助手发送测试数据")
            else:
                print("\n⚠️ 部分测试失败，请检查问题")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
