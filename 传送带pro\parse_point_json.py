#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析和格式化显示point.json文件内容
"""

import json

def parse_point_json():
    """解析point.json文件并格式化显示"""
    try:
        with open('point.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("📋 point.json文件坐标配置详情")
        print("=" * 80)
        
        for i, point in enumerate(data, 1):
            name = point.get('name', 'Unknown')
            alias = point.get('alias', '')
            joint = point.get('joint', [])
            coordinate = point.get('coordinate', [])
            tool = point.get('tool', 0)
            user = point.get('user', 0)
            aux_joint = point.get('auxJoint', [])
            
            print(f"\n{i:2d}. 点位名称: {name}")
            if alias:
                print(f"    别名: {alias}")
            print(f"    关节角度: {joint}")
            print(f"    笛卡尔坐标: {coordinate}")
            print(f"    工具: {tool}, 用户坐标系: {user}")
            if aux_joint and aux_joint != [0, 0, 0]:
                print(f"    辅助关节(传送带): {aux_joint}")
            
            # 检查是否为默认坐标
            if joint == [0, 32.2659, 32.5935, 0, 0, 0] or coordinate == [350, 0, 0, 0, 0, 0]:
                print(f"    ⚠️  警告: 使用默认坐标，可能未正确配置")
        
        print("\n" + "=" * 80)
        print("📊 统计信息:")
        print(f"   总点位数: {len(data)}")
        
        # 统计默认坐标
        default_joint_count = sum(1 for p in data if p.get('joint') == [0, 32.2659, 32.5935, 0, 0, 0])
        default_coord_count = sum(1 for p in data if p.get('coordinate') == [350, 0, 0, 0, 0, 0])
        
        print(f"   使用默认关节角度的点位: {default_joint_count}")
        print(f"   使用默认笛卡尔坐标的点位: {default_coord_count}")
        
        if default_joint_count > 0 or default_coord_count > 0:
            print("\n⚠️  发现使用默认坐标的点位:")
            for point in data:
                name = point.get('name', 'Unknown')
                joint = point.get('joint', [])
                coordinate = point.get('coordinate', [])
                
                if joint == [0, 32.2659, 32.5935, 0, 0, 0]:
                    print(f"   - {name}: 默认关节角度")
                if coordinate == [350, 0, 0, 0, 0, 0]:
                    print(f"   - {name}: 默认笛卡尔坐标")
        
        # 检查传送带位置信息
        print(f"\n🔧 传送带位置信息:")
        for point in data:
            name = point.get('name', 'Unknown')
            aux_joint = point.get('auxJoint', [])
            if aux_joint and aux_joint != [0, 0, 0]:
                print(f"   - {name}: {aux_joint[0]:.2f} mm")
        
        return data
        
    except FileNotFoundError:
        print("❌ 找不到point.json文件")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"❌ 读取文件出错: {e}")
        return None

def compare_with_lua():
    """与point.json.lua进行对比"""
    print("\n🔍 与point.json.lua的关键差异:")
    
    # 从point.json.lua中的正确坐标
    lua_coords = {
        "P_Material2_D2": [18.000000, 55.000000, 50.000000, -18.000000, 0.000000, 0.000000],
        "P_Material3_D2": [21.000000, 55.000000, 50.000000, -21.000000, 0.000000, 0.000000],
        "P_Material4_D2": [24.000000, 55.000000, 50.000000, -24.000000, 0.000000, 0.000000],
        "P_Material5_D2": [27.000000, 55.000000, 50.000000, -27.000000, 0.000000, 0.000000],
        "P_Material6_D2": [30.000000, 55.000000, 50.000000, -30.000000, 0.000000, 0.000000],
        "P_Assembly1": [-35.000000, 60.000000, 45.000000, 30.000000, 0.000000, 0.000000],
    }
    
    data = parse_point_json()
    if not data:
        return
    
    print("\n❌ point.json中配置不正确的点位:")
    for point in data:
        name = point.get('name', 'Unknown')
        joint = point.get('joint', [])
        
        if name in lua_coords:
            expected = lua_coords[name]
            if joint != expected:
                print(f"   - {name}:")
                print(f"     当前: {joint}")
                print(f"     应为: {expected}")

def main():
    """主函数"""
    print("🔍 point.json配置文件检查工具")
    print("=" * 50)
    
    data = parse_point_json()
    if data:
        compare_with_lua()
        
        print(f"\n💡 建议:")
        print(f"   1. point.json中的某些物料放置点使用了默认坐标")
        print(f"   2. 当前pro.py已使用point.json.lua的正确坐标")
        print(f"   3. 不需要修改当前代码，功能完整")
        print(f"   4. 如需更新point.json，请使用DobotStudio Pro重新标定")

if __name__ == "__main__":
    main()
