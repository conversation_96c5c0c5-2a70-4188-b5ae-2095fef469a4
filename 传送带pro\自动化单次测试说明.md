# 自动化单次测试说明

## 🎯 **修改内容**

已将自动化循环修改为**单次执行模式**，便于测试代码是否能正常执行自动化流程。

### **主要修改**：

1. **UI界面更新** 🖥️
   ```
   ┌─────────────────────────────────────┐
   │    自动化分拣控制 (测试模式)         │
   │ ⚠️ 当前设置为单次执行模式，便于测试  │
   ├─────────────────────────────────────┤
   │ 系统状态: 未初始化                   │
   │ 循环次数: 0                         │
   │                                     │
   │ 控制操作                            │
   │ [初始化系统][启动测试(单次)][停止自动化]│
   └─────────────────────────────────────┘
   ```

2. **循环逻辑修改** 🔄
   ```python
   # 原来：无限循环
   while self.automation_running:
       # 执行分拣循环
       # 等待2秒，继续下一次循环
   
   # 现在：单次执行
   if self.automation_running:
       # 执行一次分拣循环
       # 自动停止
   ```

3. **日志信息更新** 📝
   ```
   🔄 自动化测试模式：单次执行开始
   💡 测试模式：只执行一次分拣循环
   🚀 启动自动化测试流程（单次执行）...
   💡 测试模式：将执行一次完整的分拣流程后自动停止
   🎯 测试模式：第 1 次循环完成
   ✅ 单次自动化测试执行完成，自动停止
   ```

## 🧪 **测试流程**

### **测试步骤**：

1. **启动程序** 🚀
   ```
   python pro.py
   ```

2. **连接机器人** 🔗
   - 点击"连接机器人"按钮
   - 等待连接成功提示

3. **初始化自动化系统** ⚙️
   - 点击"初始化系统"按钮
   - 检查TCP连接状态
   - 确认IO端口初始化

4. **启动单次测试** 🎯
   - 点击"启动测试(单次)"按钮
   - 观察执行过程
   - 等待自动停止

### **预期执行流程**：

```
步骤1: 触发检测区1物料识别
├── 🤖 移动机械臂到拍照位置 P7
├── 📡 触发检测区1视觉检测
└── 接收物料信息: ID,X,Y,R

步骤2: 控制传送带移动到检测区1
├── 🚚 移动传送带到检测区1位置
└── 更新传送带位置显示

步骤3-4: 执行基于lua逻辑的分拣流程
├── 🤏 抓取物料 (enhanced_pick_material)
├── 🔄 物料分类判断
├── 🚚 移动到检测区2 (如果需要)
├── 📡 触发检测区2背面检测 (如果需要)
└── 📍 放置物料到目标位置

步骤5: 返回检测区1准备下一次循环
├── 🔄 移动到安全高度
├── 🚚 移动传送带回到检测区1
└── 🤖 移动机械臂到检测区1

✅ 单次自动化测试执行完成，自动停止
```

## 🔍 **测试重点观察**

### **关键检查点**：

1. **P7位置移动** ✅/❌
   - 机械臂是否能成功移动到P7拍照位置
   - 是否出现关节限位错误

2. **视觉系统通信** ✅/❌
   - TCP连接是否建立成功
   - 是否能接收到物料检测数据

3. **传送带控制** ✅/❌
   - 传送带是否能移动到指定位置
   - 位置显示是否正确更新

4. **抓取动作** ✅/❌
   - enhanced_pick_material函数是否执行成功
   - 吸盘控制是否正常

5. **放置动作** ✅/❌
   - enhanced_place_material函数是否执行成功
   - 目标点位是否可达

6. **错误处理** ✅/❌
   - 遇到错误时是否有适当的错误信息
   - 是否能安全停止

## 📊 **测试结果分析**

### **成功标志**：
- ✅ 所有步骤都能执行完成
- ✅ 没有出现关节限位错误
- ✅ 机械臂能正常移动到各个位置
- ✅ IO控制（吸盘、气泵）正常工作
- ✅ 传送带控制正常
- ✅ 程序自动停止，没有崩溃

### **失败情况**：
- ❌ P7位置移动失败（关节限位）
- ❌ 视觉系统连接失败
- ❌ 抓取动作失败（坐标问题）
- ❌ 传送带控制失败
- ❌ 程序异常退出

### **常见问题和解决方案**：

1. **P7位置失败** 🚨
   ```
   问题：❌ 机械臂移动到P7拍照位置失败
   原因：P7坐标可能超出工作空间
   解决：使用默认安全位置 [0.0, 32.29, 32.85, 0.0, 0.0, 0.0]
   ```

2. **视觉系统连接失败** 🚨
   ```
   问题：❌ TCP连接到视觉系统失败
   原因：IP地址或端口配置错误
   解决：检查网络连接，确认IP:************，端口:6005/6006
   ```

3. **抓取动作失败** 🚨
   ```
   问题：❌ enhanced_pick_material执行失败
   原因：坐标转换错误或MovL指令失败
   解决：检查坐标有效性，考虑使用相对运动
   ```

## 💡 **测试建议**

### **测试前准备**：
1. 确保机器人在安全位置
2. 检查工作空间内没有障碍物
3. 确认视觉系统已启动（如果有）
4. 准备好测试物料（如果需要）

### **测试过程中**：
1. 密切观察机械臂运动
2. 注意日志信息的颜色和内容
3. 记录任何错误信息
4. 如有异常立即点击"停止自动化"

### **测试后分析**：
1. 检查日志中的错误信息
2. 分析失败的具体步骤
3. 确定是代码逻辑问题还是硬件问题
4. 根据结果决定是否需要修改代码

## 🔄 **恢复连续模式**

如果测试成功，需要恢复连续循环模式时，可以将：
```python
if self.automation_running:  # 单次执行
```
改回：
```python
while self.automation_running:  # 连续循环
```

**现在可以开始测试了！** 🚀
