#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的机器人连接测试
用于验证机器人连接和基本移动功能
"""

import socket
import time

def send_cmd(sock, cmd):
    """发送指令到机器人"""
    try:
        full_cmd = cmd + "\n"
        print(f"📤 发送: {full_cmd.strip()}")
        
        sock.settimeout(5.0)
        sock.sendall(full_cmd.encode('utf-8'))
        response = sock.recv(1024).decode('utf-8').strip()
        print(f"📥 响应: {response}")
        
        if response and response.split(',')[0] == '0':
            return True, response
        else:
            return False, response
            
    except Exception as e:
        print(f"❌ 发送指令出错: {e}")
        return False, str(e)

def test_robot_connection():
    """测试机器人连接和基本功能"""
    robot_ip = "***********"
    dashboard_port = 29999
    motion_port = 30003
    
    print("🤖 简单机器人连接测试")
    print("=" * 40)
    
    try:
        # 1. 连接机器人
        print("🔗 连接机器人...")
        dashboard_socket = socket.create_connection((robot_ip, dashboard_port), timeout=5)
        motion_socket = socket.create_connection((robot_ip, motion_port), timeout=5)
        print("✅ 机器人连接成功")
        
        # 2. 检查机器人状态
        print("\n🔍 检查机器人状态...")
        success, response = send_cmd(dashboard_socket, "RobotMode()")
        if success:
            print("✅ 机器人状态查询成功")
        else:
            print(f"❌ 机器人状态查询失败: {response}")
        
        # 3. 使能机器人
        print("\n🔧 使能机器人...")
        success, response = send_cmd(dashboard_socket, "EnableRobot()")
        if success:
            print("✅ 机器人使能成功")
            time.sleep(2)  # 等待使能完成
        else:
            print(f"❌ 机器人使能失败: {response}")
        
        # 4. 测试基本移动（移动到安全位置）
        print("\n🤖 测试基本移动...")
        
        # 测试移动到InitialPose（安全位置）
        safe_coords = [0.0, 32.29, 32.85, 0.0, 0.0, 0.0]
        joint_cmd = f"MovJ({{{safe_coords[0]}, {safe_coords[1]}, {safe_coords[2]}, {safe_coords[3]}, {safe_coords[4]}, {safe_coords[5]}}})"
        
        success, response = send_cmd(motion_socket, joint_cmd)
        if success:
            print("✅ 移动到安全位置成功")
            
            # 等待运动完成
            send_cmd(dashboard_socket, "Sync()")
            time.sleep(2)
            
        else:
            print(f"❌ 移动到安全位置失败: {response}")
        
        # 5. 测试P7位置（point.json中的坐标）
        print("\n🎯 测试P7位置...")
        p7_coords = [43.9951, 49.8893, 54.6128, -43.4617, 0.0, 0.0]
        joint_cmd = f"MovJ({{{p7_coords[0]}, {p7_coords[1]}, {p7_coords[2]}, {p7_coords[3]}, {p7_coords[4]}, {p7_coords[5]}}})"
        
        success, response = send_cmd(motion_socket, joint_cmd)
        if success:
            print("✅ P7位置移动成功")
            
            # 等待运动完成
            send_cmd(dashboard_socket, "Sync()")
            time.sleep(2)
            
            print("💡 P7坐标有效，可以用于拍照")
            
        else:
            print(f"❌ P7位置移动失败: {response}")
            print("💡 P7坐标可能超出工作空间或有其他问题")
        
        # 6. 测试IO控制
        print("\n🔌 测试IO控制...")
        
        # 测试吸盘
        success, response = send_cmd(motion_socket, "DO(1, 1)")
        if success:
            print("✅ 吸盘开启成功")
        else:
            print(f"❌ 吸盘开启失败: {response}")
        
        time.sleep(1)
        
        success, response = send_cmd(motion_socket, "DO(1, 0)")
        if success:
            print("✅ 吸盘关闭成功")
        else:
            print(f"❌ 吸盘关闭失败: {response}")
        
        # 7. 回到安全位置
        print("\n🏠 回到安全位置...")
        joint_cmd = f"MovJ({{{safe_coords[0]}, {safe_coords[1]}, {safe_coords[2]}, {safe_coords[3]}, {safe_coords[4]}, {safe_coords[5]}}})"
        success, response = send_cmd(motion_socket, joint_cmd)
        if success:
            print("✅ 回到安全位置成功")
            send_cmd(dashboard_socket, "Sync()")
        else:
            print(f"❌ 回到安全位置失败: {response}")
        
        # 关闭连接
        dashboard_socket.close()
        motion_socket.close()
        print("\n🔌 连接已关闭")
        
        print("\n" + "=" * 40)
        print("📊 测试总结:")
        print("✅ 机器人连接正常")
        print("✅ 基本功能正常")
        print("💡 现在可以启动pro.py并测试完整功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("\n💡 建议:")
        print("1. 检查机器人是否开机")
        print("2. 检查网络连接")
        print("3. 确认IP地址是否正确")
        return False

def main():
    """主函数"""
    try:
        test_robot_connection()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断测试")
    except Exception as e:
        print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
