# 物料分拣系统更新说明

## 🔄 重要变更概述

根据您的最新需求，系统已从"两阶段检测"模式更新为"基于物料ID的智能分拣"模式。

## 📋 新的业务逻辑

### 原逻辑 ❌
- 检测区1：缺陷检测
- 检测区2：背面检测
- 分拣依据：OK/NG结果

### 新逻辑 ✅
- **6个物料芯片对应6个装配区域**（ID 1-6）
- **检测区内可能有干扰物品**
- **未识别物料ID显示为"?"**
- **分拣依据：物料ID**

## 🎯 分拣规则

| 物料ID | 分拣目标 | 说明 |
|--------|----------|------|
| 1 | 装配区1 (P1) | 物料芯片1 |
| 2 | 装配区2 (P2) | 物料芯片2 |
| 3 | 装配区3 (P3) | 物料芯片3 |
| 4 | 装配区4 (P4) | 物料芯片4 |
| 5 | 装配区5 (P5) | 物料芯片5 |
| 6 | 装配区6 (P6) | 物料芯片6 |
| "?" | 装配区1 (P_Assembly1) | 干扰物品/未识别物料 |

## 🔧 技术实现变更

### 1. 视觉系统返回格式
```
原格式：ID;状态;X;Y;角度
新格式：ID;状态;X;Y;角度

ID值变更：
- 原：0=缺陷, 1-6=正常物料
- 新：1-6=对应装配区物料, "?"=未识别物料
```

### 2. 主程序流程简化
```lua
-- 原流程：检测区1 → 抓取 → 检测区2 → 分拣
-- 新流程：检测区1 → 抓取 → 直接分拣

if id_num == 1 then
    place_material(P1, pos_r)
elseif id_num == 2 then
    place_material(P2, pos_r)
-- ... 其他ID
else
    place_material(P_Assembly1, pos_r)  -- 未识别物料
end
```

### 3. 数据处理改进
```lua
-- 支持字符串ID（包括"?"）
local id = parts[1]  -- 保持为字符串
local id_num = tonumber(material_id)  -- 尝试转换为数字

-- 如果转换失败（如"?"），id_num为nil，进入else分支
```

## 📁 更新的文件

### 主要修改：
1. **material_sorting_system.lua**
   - 简化工作流程
   - 移除检测区2逻辑
   - 添加6个装配区分拣逻辑
   - 支持"?"ID处理

2. **global_new.lua**
   - 修改视觉数据解析函数
   - 支持字符串ID格式



4. **使用说明_新系统.md**
   - 更新系统概述
   - 修改工作流程图
   - 更新配置说明

## 🚀 使用指南

### 1. 视觉软件配置
确保您的视觉软件能够：
- 识别6种不同的物料芯片（返回ID 1-6）
- 对于无法识别的物料或干扰物品返回ID "?"
- 返回格式：`ID;状态;X;Y;角度`

### 2. 点位配置
确保以下点位已正确示教：
- P1-P6：对应6个装配区
- P_Assembly1：干扰物品专用区域

### 3. 测试建议
1. 先测试已知物料（ID 1-6）的分拣
2. 再测试干扰物品（ID "?"）的处理
3. 验证所有装配区的放置精度

## ⚠️ 注意事项

### 兼容性保持：
- 保留了检测区2的连接（端口6006）以保持兼容性
- 原有的点位P1-P6继续使用
- 可以随时回退到原系统

### 关键改进：
- ✅ 支持6个装配区的精确分拣
- ✅ 智能处理干扰物品
- ✅ 简化了工作流程
- ✅ 提高了分拣效率

## 🔍 故障排除

### 常见问题：
1. **ID显示为"?"过多**
   - 检查视觉算法的识别精度
   - 确认物料芯片的特征库是否完整

2. **分拣到错误装配区**
   - 验证视觉系统返回的ID值
   - 检查点位P1-P6的坐标

3. **干扰物品处理异常**
   - 确认P_Assembly1点位是否正确
   - 检查"?"字符的处理逻辑

## 📞 技术支持

如有问题，请：
1. 查看控制台日志输出
2. 运行test_new_system.lua进行诊断
3. 参考使用说明_新系统.md
4. 检查视觉软件的返回数据格式
