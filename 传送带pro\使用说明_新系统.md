# 物料分拣系统使用说明（新版本）

## 系统概述
本系统实现了两阶段检测的智能分拣流程：
1. **检测区1**：物料识别（发送"ok"指令）
2. **传送带控制**：移动到检测区1正前方（已完成标定板标定）
3. **机械臂抓取**：吸取物料
4. **分拣判断**：
   - **部分物料**：在第一次检测时直接分拣到装配区1（如ID=1或未识别物料）
   - **其他物料**：进入第二阶段检测
5. **检测区2**：传送带移动到检测区2，发送"start"指令进行背面缺陷检测
6. **最终分拣**：根据OK/NG结果分拣到装配区1或装配区2

## 文件结构

### 新创建的文件：
1. **material_sorting_system.lua** - 主程序文件
2. **global_new.lua** - 全局函数库（支持双端口）
3. **test_new_system.lua** - 系统测试程序
4. **prj_new.json** - 项目配置文件
5. **使用说明_新系统.md** - 本说明文档

### 保留的原文件：
- **src0.lua** - 原主程序（已保留）
- **global.lua** - 原全局函数（已保留）
- **point.json** - 点位配置（共用）
- **prj.json** - 原项目配置（已保留）

## 使用步骤

### 1. 在DobotStudio Pro中导入项目

#### 方法A：创建新项目
1. 打开DobotStudio Pro
2. 创建新项目
3. 将以下文件导入项目：
   - `material_sorting_system.lua`
   - `global_new.lua`
   - `point.json`
   - `prj_new.json`

#### 方法B：在现有项目中测试
1. 将新文件复制到现有项目文件夹
2. 在DobotStudio Pro中刷新文件列表

### 2. 配置点位坐标

在DobotStudio Pro中示教以下点位：

```
P7 (P_Camera_Pos)     - 拍照/准备位置
P_Detection1          - 检测区1上方位置
P_Detection2          - 检测区2上方位置
P_Safe_Height         - 安全高度点位（检测区2上方100mm）

检测区1点位：
P_Waste_Detection1    - 检测区1废料放置点

检测区2点位（6个物料放置点）：
P_Material1_D2        - 检测区2物料1放置点
P_Material2_D2        - 检测区2物料2放置点
P_Material3_D2        - 检测区2物料3放置点
P_Material4_D2        - 检测区2物料4放置点
P_Material5_D2        - 检测区2物料5放置点
P_Material6_D2        - 检测区2物料6放置点

最终废料区：
P_Assembly1           - 装配区1（最终废料区）
```

### 3. 配置传送带参数

在 `global_new.lua` 中调整传送带参数：

```lua
local CONVEYOR_DETECTION1_POS = 0      -- 检测区1位置（mm）
local CONVEYOR_DETECTION2_POS = 500    -- 检测区2位置（mm）
local CONVEYOR_SPEED = 80               -- 运动速度（1-100）
local CONVEYOR_ACC = 50                 -- 运动加速度（1-100）
```

### 4. 配置视觉系统通信（双端口模式）

您需要在视觉软件中建立两个服务端：

#### 检测区1服务端（端口6005）：
- 监听端口：6005
- 接收指令：`"ok"`
- 返回格式：`ID;状态;X;Y;角度`
- 功能：物料识别
- ID值说明：
  - `1`：需要直接分拣到装配区1的物料
  - `2-6`：需要进行背面检测的物料
  - `"?"`：未识别物料或干扰物品（直接分拣到装配区1）

#### 检测区2服务端（端口6006）：
- 监听端口：6006
- 接收指令：`"start"`
- 返回格式：`OK` 或 `NG`
- 功能：从下向上识别物料背面是否有缺陷
- 检测时机：物料被吸取并移动到检测区2时触发

### 5. 测试系统

#### 步骤1：运行功能测试
```
运行文件：test_new_system.lua
```
这将测试：
- TCP连接
- 传送带控制
- 视觉通信
- 点位验证
- IO端口控制

#### 步骤2：运行主程序
```
运行文件：material_sorting_system.lua
```

## 系统工作流程

### 完整流程图：
```
开始
  ↓
移动到拍照位置
  ↓
发送"ok"指令 → 检测区1物料识别
  ↓
接收坐标数据 (ID, X, Y, R)
  ↓
传送带→检测区1前方
  ↓
判断物料类型？
  ↓                    ↓
未识别物料            物料芯片
(ID=?)               (ID=1-6)
  ↓                    ↓
抓取并放置到          吸住物料（不松开）
检测区1废料点         ↓
  ↓                 传送带→检测区2
循环继续              ↓
                   移动到检测区2上方
                      ↓
                   发送"start"指令 → 背面缺陷检测
                      ↓
                   接收OK/NG结果
                      ↓
                   判断：OK？
                      ↓        ↓
                    是         否
                      ↓        ↓
              直接放置到装配区2   先抬高机械臂
              对应ID槽位        ↓
                      ↓        返回检测区1
              抬高机械臂        ↓
                      ↓        放置到装配区1
              返回检测区1       （废料区）
              开始下一轮        ↓
                      ↓        循环继续
                      循环继续
```

### 分拣逻辑：
- **检测区1废料处理**：未识别物料(ID="?") → 检测区1废料点
- **检测区2合格品处理**：背面检测OK → 直接放置到装配区2对应ID槽 → 抬高机械臂 → 返回检测区1开始下一轮
- **检测区2不合格品处理**：背面检测NG → 抬高机械臂 → 返回检测区1 → 放置到装配区1（废料区）
- **安全移动**：无论OK还是NG，都需要先抬高机械臂再进行平移移动

## 传送带控制说明

### MovJExt指令使用：
```lua
local option = {SpeedE = 80, AccE = 50, SYNC = 1}
MovJExt(500, option)  -- 移动到500mm位置
SyncAll()             -- 等待运动完成
```

### 参数说明：
- **SpeedE**: 运动速度比例（1-100）
- **AccE**: 运动加速度比例（1-100）
- **SYNC**: 同步标识（建议设为1）

## 安全注意事项

### 运行前检查：
1. ✅ 所有点位已正确示教
2. ✅ 传送带位置参数正确
3. ✅ 视觉系统通信正常
4. ✅ IO端口接线正确
5. ✅ 机械臂运动路径安全

### 调试建议：
1. **先运行测试程序** - 验证各功能模块
2. **逐步调试** - 从单个功能到完整流程
3. **降低速度** - 首次运行时降低机械臂和传送带速度
4. **监控日志** - 观察控制台输出信息

## 故障排除

### 常见问题：

#### 1. TCP连接失败
```
检查：IP地址、端口号、网络连接
解决：修改global_new.lua中的ip和port参数
```

#### 2. 传送带不移动
```
检查：MovJExt指令参数、扩展轴配置
解决：确认DobotStudio Pro中扩展轴设置正确
```

#### 3. 视觉数据解析失败
```
检查：数据格式、分隔符
解决：确认视觉系统返回格式为 "ID;状态;X;Y;角度"
```

#### 4. 机械臂运动异常
```
检查：点位坐标、工具坐标系
解决：重新示教点位，检查坐标系设置
```

## 参数调整

### 传送带速度调整：
```lua
set_conveyor_speed(60, 40)  -- 速度60%, 加速度40%
```

### 传送带位置调整：
```lua
set_conveyor_positions(100, 600)  -- 检测区1:100mm, 检测区2:600mm
```

### 超时时间调整：
在视觉检测函数中修改 `max_timeout` 参数

## 日志信息

系统会输出详细的日志信息：
- **INFO**: 正常操作信息
- **WARN**: 警告信息
- **ERROR**: 错误信息

监控这些信息有助于调试和故障排除。

## 视觉软件配置要求

### 您需要在视觉软件中配置：

#### 检测区1服务端（端口6005）：
- 监听端口：6005
- 接收指令：`"ok"`
- 返回格式：`ID;状态;X;Y;角度`
- 返回示例：
  - `"1;OK;250.5;100.2;45.0"` （直接分拣到装配区1的物料）
  - `"2;OK;250.5;100.2;45.0"` （需要背面检测的物料）
  - `"?;UNKNOWN;250.5;100.2;45.0"` （未识别物料，直接分拣到装配区1）

#### 检测区2服务端（端口6006）：
- 监听端口：6006
- 接收指令：`"start"`
- 返回格式：`OK` 或 `NG`
- 功能：从下向上识别物料背面缺陷
- 返回说明：
  - `"OK"`：背面无缺陷，分拣到装配区2
  - `"NG"`：背面有缺陷，分拣到装配区1

## 技术支持

如遇到问题，请：
1. 检查日志输出
2. 运行测试程序诊断
3. 参考故障排除章节
4. 验证视觉软件返回数据格式
5. 保留原文件作为备份
