#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版视觉测试程序 - 测试基本功能
"""

import sys
import socket
import threading
import time
from datetime import datetime

def test_imports():
    """测试所需模块的导入"""
    print("🧪 测试模块导入...")
    
    try:
        import tkinter as tk
        print("✅ tkinter 导入成功")
    except ImportError as e:
        print(f"❌ tkinter 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ numpy 导入成功 (版本: {np.__version__})")
    except ImportError as e:
        print(f"❌ numpy 导入失败: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV 导入成功 (版本: {cv2.__version__})")
    except ImportError as e:
        print(f"❌ OpenCV 导入失败: {e}")
        print("请运行: python -m pip install opencv-python")
        return False
    
    try:
        from PIL import Image, ImageTk
        print(f"✅ PIL 导入成功")
    except ImportError as e:
        print(f"❌ PIL 导入失败: {e}")
        print("请运行: python -m pip install pillow")
        return False
    
    return True

def test_vision_workpiece_detector():
    """测试vision_workpiece_detector模块"""
    print("\n🧪 测试vision_workpiece_detector模块...")
    
    try:
        from vision_workpiece_detector import VisionWorkpieceDetector
        print("✅ VisionWorkpieceDetector 导入成功")
        
        # 尝试创建实例
        detector = VisionWorkpieceDetector("vision_config.json")
        print("✅ VisionWorkpieceDetector 实例创建成功")
        return True
        
    except ImportError as e:
        print(f"❌ VisionWorkpieceDetector 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ VisionWorkpieceDetector 实例创建失败: {e}")
        return False

def create_simple_gui():
    """创建简单的GUI测试界面"""
    print("\n🖥️ 创建简单GUI测试界面...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        root = tk.Tk()
        root.title("视觉系统测试")
        root.geometry("600x400")
        
        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 状态标签
        status_label = ttk.Label(main_frame, text="视觉系统测试界面", font=("Arial", 14))
        status_label.pack(pady=10)
        
        # 测试按钮
        test_frame = ttk.LabelFrame(main_frame, text="测试功能")
        test_frame.pack(fill=tk.X, pady=10)
        
        def test_opencv():
            try:
                import cv2
                import numpy as np
                # 创建一个测试图像
                img = np.zeros((100, 100, 3), dtype=np.uint8)
                cv2.circle(img, (50, 50), 30, (0, 255, 0), 2)
                status_label.config(text="OpenCV测试成功！")
            except Exception as e:
                status_label.config(text=f"OpenCV测试失败: {e}")
        
        ttk.Button(test_frame, text="测试OpenCV", command=test_opencv).pack(pady=5)
        
        def test_tcp_server():
            try:
                # 测试TCP服务器
                server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                server_socket.bind(('localhost', 6005))
                server_socket.close()
                status_label.config(text="TCP服务器测试成功！")
            except Exception as e:
                status_label.config(text=f"TCP服务器测试失败: {e}")
        
        ttk.Button(test_frame, text="测试TCP服务器", command=test_tcp_server).pack(pady=5)
        
        # 日志文本框
        log_frame = ttk.LabelFrame(main_frame, text="日志")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        log_text = tk.Text(log_frame, height=10)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
        log_text.configure(yscrollcommand=scrollbar.set)
        
        log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加初始日志
        log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] 视觉系统测试界面启动\n")
        log_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] 点击按钮进行功能测试\n")
        
        print("✅ GUI界面创建成功")
        
        # 运行GUI
        print("🚀 启动GUI界面...")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")

def main():
    """主函数"""
    print("🚀 开始视觉系统测试")
    print("=" * 50)
    
    # 测试模块导入
    if not test_imports():
        print("\n❌ 模块导入测试失败，请安装缺失的依赖包")
        print("建议运行以下命令:")
        print("python -m pip install opencv-python pillow numpy")
        return
    
    # 测试vision_workpiece_detector
    detector_ok = test_vision_workpiece_detector()
    
    if detector_ok:
        print("\n✅ 所有模块测试通过！")
        print("🚀 启动完整的vision_demo.py...")
        
        # 尝试启动完整的vision_demo
        try:
            from vision_demo import VisionDemoGUI
            app = VisionDemoGUI()
            app.run()
        except Exception as e:
            print(f"❌ 启动vision_demo失败: {e}")
            print("🔄 启动简化版GUI...")
            create_simple_gui()
    else:
        print("\n⚠️ vision_workpiece_detector模块有问题")
        print("🔄 启动简化版GUI进行基础测试...")
        create_simple_gui()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
