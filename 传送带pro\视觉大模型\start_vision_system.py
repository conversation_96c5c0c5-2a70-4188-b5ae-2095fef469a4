#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DobotVisionStudio 视觉系统启动脚本

这个脚本提供了多种启动方式：
1. GUI演示程序 (集成硬件连接)
2. 纯Python检测器
3. 硬件连接测试
4. 配置检查
"""

import sys
import os
import json
import socket
import time
from datetime import datetime

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'cv2', 'numpy', 'PIL', 'tkinter'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装:")
        print("pip install opencv-python numpy pillow")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_config():
    """检查配置文件"""
    config_file = "vision_config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置项
        required_keys = [
            'communication.hardware_connection.vision_hardware_ip',
            'communication.hardware_connection.vision_server_port',
            'communication.hardware_connection.vision_trigger_port'
        ]
        
        for key in required_keys:
            keys = key.split('.')
            value = config
            for k in keys:
                if k not in value:
                    print(f"❌ 配置项缺失: {key}")
                    return False
                value = value[k]
        
        print("✅ 配置文件检查通过")
        print(f"   - 硬件IP: {config['communication']['hardware_connection']['vision_hardware_ip']}")
        print(f"   - 服务端口: {config['communication']['hardware_connection']['vision_server_port']}")
        print(f"   - 触发端口: {config['communication']['hardware_connection']['vision_trigger_port']}")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def test_hardware_connection():
    """测试硬件连接"""
    print("🔍 测试视觉硬件连接...")
    
    # 加载配置
    try:
        with open("vision_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        hardware_config = config['communication']['hardware_connection']
        ip = hardware_config['vision_hardware_ip']
        trigger_port = hardware_config['vision_trigger_port']
        server_port = hardware_config['vision_server_port']
        
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return False
    
    # 测试触发端口连接
    print(f"📡 测试触发端口连接 ({ip}:{trigger_port})...")
    try:
        with socket.create_connection((ip, trigger_port), timeout=3) as s:
            print("✅ 触发端口连接成功")
            trigger_ok = True
    except Exception as e:
        print(f"❌ 触发端口连接失败: {e}")
        trigger_ok = False
    
    # 测试服务端口绑定
    print(f"🔌 测试服务端口绑定 (0.0.0.0:{server_port})...")
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        test_socket.bind(('0.0.0.0', server_port))
        test_socket.close()
        print("✅ 服务端口绑定成功")
        server_ok = True
    except Exception as e:
        print(f"❌ 服务端口绑定失败: {e}")
        server_ok = False
    
    return trigger_ok and server_ok

def start_gui_demo():
    """启动GUI演示程序"""
    print("🖥️ 启动GUI演示程序...")
    try:
        from vision_demo import VisionDemoGUI
        app = VisionDemoGUI()
        app.run()
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        print("请确保 vision_demo.py 文件存在")
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")

def start_detector_only():
    """启动纯检测器"""
    print("🔍 启动Python检测器...")
    try:
        from vision_workpiece_detector import VisionWorkpieceDetector
        
        detector = VisionWorkpieceDetector("vision_config.json")
        detector.start_tcp_server(8888)
        
        print("✅ 检测器已启动，TCP服务器运行在端口 8888")
        print("按 Ctrl+C 退出")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🔄 正在关闭检测器...")
            detector.stop_tcp_server()
            
    except ImportError as e:
        print(f"❌ 导入检测器模块失败: {e}")
        print("请确保 vision_workpiece_detector.py 文件存在")
    except Exception as e:
        print(f"❌ 启动检测器失败: {e}")

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🤖 DobotVisionStudio 视觉识别系统")
    print("="*60)
    print("请选择启动方式:")
    print("1. GUI演示程序 (推荐)")
    print("2. 纯Python检测器")
    print("3. 硬件连接测试")
    print("4. 配置检查")
    print("5. 依赖检查")
    print("0. 退出")
    print("="*60)

def main():
    """主函数"""
    print(f"🚀 DobotVisionStudio 视觉系统启动器")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请输入选择 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
                
            elif choice == '1':
                print("\n" + "-"*40)
                if check_dependencies() and check_config():
                    start_gui_demo()
                else:
                    print("❌ 环境检查失败，无法启动GUI")
                    
            elif choice == '2':
                print("\n" + "-"*40)
                if check_dependencies() and check_config():
                    start_detector_only()
                else:
                    print("❌ 环境检查失败，无法启动检测器")
                    
            elif choice == '3':
                print("\n" + "-"*40)
                test_hardware_connection()
                
            elif choice == '4':
                print("\n" + "-"*40)
                check_config()
                
            elif choice == '5':
                print("\n" + "-"*40)
                check_dependencies()
                
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
